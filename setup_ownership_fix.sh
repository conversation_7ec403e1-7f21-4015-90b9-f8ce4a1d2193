#!/bin/bash

echo "设置文件所有权自动修复"
echo "================================"

# 设置脚本权限
chmod +x fix_runtime_ownership.sh
chmod +x auto_fix_ownership.sh

# 立即修复一次
echo "立即修复runtime目录文件所有权..."
./fix_runtime_ownership.sh

# 询问是否设置定时任务
echo ""
read -p "是否设置定时任务每10分钟自动修复文件所有权? (y/n): " setup_cron

if [[ $setup_cron =~ ^[Yy]$ ]]; then
    PROJECT_DIR=$(pwd)
    CRON_JOB="*/10 * * * * $PROJECT_DIR/auto_fix_ownership.sh"
    
    # 检查是否已存在该定时任务
    if crontab -l 2>/dev/null | grep -q "auto_fix_ownership.sh"; then
        echo "定时任务已存在"
    else
        # 添加定时任务
        (crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -
        echo "✅ 定时任务已添加: 每10分钟自动修复文件所有权"
    fi
fi

echo ""
echo "设置完成！"
echo ""
echo "现在您可以："
echo "1. 继续使用root用户启动队列: ./start_queue_listener.sh"
echo "2. 手动修复文件所有权: ./fix_runtime_ownership.sh"
echo "3. 文件所有权会自动修复（如果设置了定时任务）"
echo ""
echo "查看修复日志: tail -f runtime/logs/ownership_fix.log"
