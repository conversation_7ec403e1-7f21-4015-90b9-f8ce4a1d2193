<?php

namespace app\common\model;

use think\Model;

/**
 * 资产变化日志模型
 */
class EsopAssetChangeLog extends Model
{
    // 表名
    protected $name = 'esop_asset_change_logs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    
    // 追加属性
    protected $append = [
        
    ];
    
    /**
     * 记录资产变化日志
     * 
     * @param int $userId 用户ID
     * @param int $bAccountId B端账户ID
     * @param int $stockId 股票ID
     * @param float $amount 金额/股数
     * @param string $actionName 操作名称
     * @param string $extraName 额外名称
     * @param string $subName 子名称
     * @param int $isAudit 是否需要审批：0-否，1-是
     * @param int $auditStatus 审批状态：0-待审批，1-通过，2-不通过
     * @param array|string $extends 扩展数据，可以是数组或JSON字符串
     * @param int $relationId 关联ID
     * @param string $createdAt 创建时间
     * @return EsopAssetChangeLog
     */
    public static function recordAssetChange($userId,$bAccountId, $stockId, $amount, $actionName, $extraName, $subName, $isAudit = 0, $auditStatus = 0, $extends = [], $relationId = 0, $createdAt = null)
    {
        $log = new self();
        $log->user_id = $userId;
        $log->b_account_id = $bAccountId;
        $log->stock_id = $stockId;
        $log->amount = $amount;
        $log->action_name = $actionName;
        $log->extra_name = $extraName;
        $log->sub_name = $subName;
        $log->is_audit = $isAudit;
        $log->audit_status = $auditStatus;
        $log->extends = is_array($extends) ? json_encode($extends, JSON_UNESCAPED_UNICODE) : $extends;
        $log->created_at = $createdAt ?? date('Y-m-d H:i:s');
        $log->relation_id = $relationId;
        $log->save();
        
        return $log;
    }
    
    /**
     * 批量记录资产变化日志
     * 
     * @param array $logsData 日志数据数组，每条记录包含：
     *        - user_id: 用户ID
     *        - b_account_id: B端账户ID
     *        - stock_id: 股票ID
     *        - amount: 金额/股数
     *        - action_name: 操作名称
     *        - extra_name: 额外名称
     *        - sub_name: 子名称
     *        - is_audit: 是否需要审批
     *        - audit_status: 审批状态
     *        - extends: 扩展数据，可以是数组或JSON字符串   
     *        - relation_id: 关联ID
     * @return boolean 是否添加成功
     */
    public static function batchRecordAssetChange($logsData)
    {
        if (empty($logsData)) {
            return false;
        }
        
        $logs = [];
        $now = date('Y-m-d H:i:s');
        
        foreach ($logsData as $data) {
            $logs[] = [
                'user_id' => $data['user_id'],
                'b_account_id' => $data['b_account_id'],
                'stock_id' => $data['stock_id'],
                'amount' => $data['amount'],
                'action_name' => $data['action_name'],
                'extra_name' => $data['extra_name'],
                'sub_name' => $data['sub_name'],
                'is_audit' => isset($data['is_audit']) ? $data['is_audit'] : 0,
                'audit_status' => isset($data['audit_status']) ? $data['audit_status'] : 0,
                'extends' => isset($data['extends']) ? (is_array($data['extends']) ? json_encode($data['extends'], JSON_UNESCAPED_UNICODE) : $data['extends']) : '{}',
                'created_at' => $data['created_at'] ?? $now,
                'relation_id' => $data['relation_id'] ?? 0
            ];
        }
        
        // 批量插入记录
        return (new self())->saveAll($logs);
    }
    
    /**
     * 获取用户资产变化日志列表
     * 
     * @param int $userId 用户ID
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public static function getUserAssetChangeLogs($userId, $page = 1, $pageSize = 20)
    {
        return self::where('user_id', $userId)
            ->order('created_at', 'desc')
            ->paginate($pageSize, false, ['page' => $page]);
    }
    
    /**
     * 根据操作名称获取资产变化日志
     * 
     * @param int $userId 用户ID
     * @param string $actionName 操作名称
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public static function getLogsByActionName($userId, $actionName, $page = 1, $pageSize = 20)
    {
        return self::where('user_id', $userId)
            ->where('action_name', $actionName)
            ->order('created_at', 'desc')
            ->paginate($pageSize, false, ['page' => $page]);
    }
    
    /**
     * 获取指定时间范围内的资产变化日志
     * 
     * @param int $userId 用户ID
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public static function getLogsByTimeRange($userId, $startTime, $endTime, $page = 1, $pageSize = 20)
    {
        return self::where('user_id', $userId)
            ->whereTime('created_at', 'between', [$startTime, $endTime])
            ->order('created_at', 'desc')
            ->paginate($pageSize, false, ['page' => $page]);
    }
    
    /**
     * 获取待审批的资产变化日志
     * 
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public static function getPendingAuditLogs($page = 1, $pageSize = 20)
    {
        return self::where('is_audit', 1)
            ->where('audit_status', 0)
            ->order('created_at', 'desc')
            ->paginate($pageSize, false, ['page' => $page]);
    }
    
    /**
     * 获取指定B端账户的资产变化日志
     * 
     * @param int $bAccountId B端账户ID
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public static function getLogsByBAccount($bAccountId, $page = 1, $pageSize = 20)
    {
        return self::where('b_account_id', $bAccountId)
            ->order('created_at', 'desc')
            ->paginate($pageSize, false, ['page' => $page]);
    }
    
    /**
     * 获取指定股票的资产变化日志
     * 
     * @param int $stockId 股票ID
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     */
    public static function getLogsByStock($stockId, $page = 1, $pageSize = 20)
    {
        return self::where('stock_id', $stockId)
            ->order('created_at', 'desc')
            ->paginate($pageSize, false, ['page' => $page]);
    }
    
    /**
     * 更新审批状态
     * 
     * @param int $logId 日志ID
     * @param int $auditStatus 审批状态：1-通过，2-不通过
     * @return boolean
     */
    public static function updateAuditStatus($logId, $auditStatus)
    {
        return self::where('id', $logId)->update(['audit_status' => $auditStatus]);
    }
    
    /**
     * 获取审批状态文本
     * 
     * @param int $auditStatus 审批状态
     * @return string
     */
    public static function getAuditStatusText($auditStatus)
    {
        switch ($auditStatus) {
            case 0:
                return '待审批';
            case 1:
                return '已通过';
            case 2:
                return '已拒绝';
            default:
                return '未知状态';
        }
    }
    
    /**
     * 获取是否需要审批文本
     * 
     * @param int $isAudit 是否需要审批
     * @return string
     */
    public static function getIsAuditText($isAudit)
    {
        return $isAudit == 1 ? '需要审批' : '无需审批';
    }
    
    /**
     * 获取扩展数据
     * 
     * @param string $extends JSON字符串
     * @return array
     */
    public function getExtendsAttribute($extends)
    {
        if (empty($extends)) {
            return [];
        }
        
        $data = json_decode($extends, true);
        return is_array($data) ? $data : [];
    }
    
    /**
     * 设置扩展数据
     * 
     * @param array|string $extends 扩展数据
     * @return string
     */
    public function setExtendsAttribute($extends)
    {
        if (is_array($extends)) {
            return json_encode($extends, JSON_UNESCAPED_UNICODE);
        }
        
        return $extends;
    }
    
    /**
     * 获取扩展数据中的指定字段
     * 
     * @param string $key 字段名
     * @param mixed $default 默认值
     * @return mixed
     */
    public function getExtendsValue($key, $default = null)
    {
        $extends = $this->extends;
        return isset($extends[$key]) ? $extends[$key] : $default;
    }
    
    /**
     * 设置扩展数据中的指定字段
     * 
     * @param string $key 字段名
     * @param mixed $value 字段值
     * @return boolean
     */
    public function setExtendsValue($key, $value)
    {
        $extends = $this->extends;
        $extends[$key] = $value;
        $this->extends = $extends;
        return $this->save();
    }

    public static function getRowByRelationId($relationId,$actionName)
    {
        return self::where('relation_id', $relationId)->where('action_name', $actionName)->find();
    }

    public static function getRowsByRelationId($relationId,$actionName)
    {
        return self::where('relation_id', $relationId)->where('action_name', $actionName)->select();
    }
} 