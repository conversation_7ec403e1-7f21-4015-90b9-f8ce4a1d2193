#!/bin/bash

echo "文件所有权监听服务状态检查"
echo "================================"

# 检查进程是否在运行
MONITOR_PIDS=$(pgrep -f "ownership_monitor.sh")

# 验证找到的PID是否真的存在
VALID_PIDS=""
if [ -n "$MONITOR_PIDS" ]; then
    for PID in $MONITOR_PIDS; do
        if kill -0 $PID 2>/dev/null; then
            VALID_PIDS="$VALID_PIDS $PID"
        fi
    done
fi

if [ -n "$VALID_PIDS" ]; then
    echo "✅ 监听服务正在运行"
    echo "PID:$VALID_PIDS"
    
    # 显示进程详细信息
    echo ""
    echo "进程详情:"
    ps aux | grep ownership_monitor | grep -v grep
    
    # 检查PID文件
    if [ -f "runtime/ownership_monitor.pid" ]; then
        SAVED_PID=$(cat runtime/ownership_monitor.pid)
        echo ""
        echo "保存的PID: $SAVED_PID"
        if echo "$VALID_PIDS" | grep -q "$SAVED_PID"; then
            echo "✅ PID文件与运行进程匹配"
        else
            echo "⚠️  PID文件与运行进程不匹配"
            # 检查保存的PID是否真的存在
            if kill -0 $SAVED_PID 2>/dev/null; then
                echo "   保存的PID $SAVED_PID 仍在运行，但可能不是监听进程"
            else
                echo "   保存的PID $SAVED_PID 已不存在，建议清理PID文件"
            fi
        fi
    else
        echo "⚠️  未找到PID文件"
    fi
    
    # 检查日志文件
    echo ""
    echo "日志文件状态:"
    if [ -f "runtime/logs/ownership_monitor.log" ]; then
        LOG_SIZE=$(wc -l < runtime/logs/ownership_monitor.log)
        echo "✅ 监听日志: $LOG_SIZE 行"
        echo "最近的日志:"
        tail -3 runtime/logs/ownership_monitor.log
    else
        echo "⚠️  未找到监听日志文件"
    fi
    
    if [ -f "runtime/logs/ownership_monitor_output.log" ]; then
        echo "✅ 输出日志存在"
    else
        echo "⚠️  未找到输出日志文件"
    fi
    
else
    echo "❌ 监听服务未运行"
    
    # 检查是否有残留的PID文件
    if [ -f "runtime/ownership_monitor.pid" ]; then
        echo "发现残留PID文件，正在清理..."
        rm runtime/ownership_monitor.pid
        echo "✅ PID文件已清理"
    fi
fi

echo ""
echo "可用操作:"
echo "1. 查看实时日志: tail -f runtime/logs/ownership_monitor.log"
echo "2. 停止服务: ./stop_ownership_monitor.sh"
echo "3. 重启服务: ./stop_ownership_monitor.sh && ./start_ownership_monitor.sh"
echo "4. 前台运行: ./start_ownership_monitor.sh --foreground"
