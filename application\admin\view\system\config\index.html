<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs" data-field="configtype">
            <li class="active"><a href="#basic" data-toggle="tab">{:__('基础设置')}</a></li>
            <li><a href="#third" data-toggle="tab">{:__('第三方设置')}</a></li>
            <li><a href="#about" data-toggle="tab">{:__('关于我们')}</a></li>
            <!-- 新增注销风险提示Tab -->
            <li><a href="#delete_risk" data-toggle="tab">{:__('注销风险提示')}</a></li>
            <!-- 新增隐私政策Tab -->
            <li><a href="#privacy_policy" data-toggle="tab">{:__('隐私政策')}</a></li>
            <!-- 新增用户协议Tab -->
            <li><a href="#user_agreement" data-toggle="tab">{:__('用户协议')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="basic">
                <div class="widget-body no-padding">
                    <form id="config-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('system.config/index')}">
                        
                        <div class="form-group">
                            <label for="company_name" class="control-label col-xs-12 col-sm-2">{:__('名称')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input type="text" class="form-control" id="company_name" name="row[company_name]" value="{$row.company_name|default=''}" data-rule="required" />
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="logo" class="control-label col-xs-12 col-sm-2">{:__('LOGO')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <div class="input-group">
                                    <input id="c-logo" class="form-control" size="50" name="row[logo_url]" type="text" value="{$row.logo_url|default=''}">
                                    <div class="input-group-addon no-border no-padding">
                                        <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-logo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false"><i class="fa fa-upload"></i> {:__('上传')}</button></span>
                                        <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-logo" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('选择')}</button></span>
                                    </div>
                                    <span class="msg-box n-right" for="c-logo"></span>
                                </div>
                                <ul class="row list-inline faupload-preview" id="p-logo"></ul>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="introduction" class="control-label col-xs-12 col-sm-2">{:__('简介')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea class="form-control" id="introduction" name="row[introduction]" rows="5">{$row.introduction|default=''}</textarea>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="website_url" class="control-label col-xs-12 col-sm-2">{:__('网址')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input type="text" class="form-control" id="website_url" name="row[website_url]" value="{$row.website_url|default=''}" />
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('APP版本管理')}:</label>
                            <div class="col-xs-12 col-sm-10">
                                <!-- 安卓版本管理 -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-android text-success"></i> 安卓版本管理
                                            <button type="button" class="btn btn-success btn-xs pull-right" id="upload-android-version">
                                                <i class="fa fa-upload"></i> 上传新版本
                                            </button>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>版本名称</th>
                                                        <th>版本号</th>
                                                        <th>文件大小</th>
                                                        <th>是否当前版本</th>
                                                        <th>强制更新</th>
                                                        <th>上传时间</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="android-versions-list">
                                                    {volist name="androidVersions" id="version"}
                                                    <tr data-id="{$version.id}">
                                                        <td>{$version.version_name}</td>
                                                        <td>{$version.version_code}</td>
                                                        <td>{$version.file_size_text}</td>
                                                        <td>
                                                            {if $version.is_current}
                                                            <span class="label label-success">当前版本</span>
                                                            {else}
                                                            <span class="label label-default">历史版本</span>
                                                            {/if}
                                                        </td>
                                                        <td>
                                                            {if $version.is_force_update}
                                                            <span class="label label-danger">强制</span>
                                                            {else}
                                                            <span class="label label-info">普通</span>
                                                            {/if}
                                                        </td>
                                                        <td>{$version.created_at}</td>
                                                        <td>
                                                            <a href="{$version.file_url}" target="_blank" class="btn btn-xs btn-primary">下载</a>
                                                            {if !$version.is_current}
                                                            <button type="button" class="btn btn-xs btn-success set-current-btn" data-id="{$version.id}">设为当前</button>
                                                            <button type="button" class="btn btn-xs btn-danger delete-version-btn" data-id="{$version.id}">删除</button>
                                                            {/if}
                                                        </td>
                                                    </tr>
                                                    {/volist}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- iOS版本管理 -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-apple text-info"></i> iOS版本管理
                                            <button type="button" class="btn btn-info btn-xs pull-right" id="upload-ios-version">
                                                <i class="fa fa-upload"></i> 上传新版本
                                            </button>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>版本名称</th>
                                                        <th>版本号</th>
                                                        <th>文件大小</th>
                                                        <th>是否当前版本</th>
                                                        <th>强制更新</th>
                                                        <th>上传时间</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="ios-versions-list">
                                                    {volist name="iosVersions" id="version"}
                                                    <tr data-id="{$version.id}">
                                                        <td>{$version.version_name}</td>
                                                        <td>{$version.version_code}</td>
                                                        <td>{$version.file_size_text}</td>
                                                        <td>
                                                            {if $version.is_current}
                                                            <span class="label label-success">当前版本</span>
                                                            {else}
                                                            <span class="label label-default">历史版本</span>
                                                            {/if}
                                                        </td>
                                                        <td>
                                                            {if $version.is_force_update}
                                                            <span class="label label-danger">强制</span>
                                                            {else}
                                                            <span class="label label-info">普通</span>
                                                            {/if}
                                                        </td>
                                                        <td>{$version.created_at}</td>
                                                        <td>
                                                            <a href="{$version.file_url}" target="_blank" class="btn btn-xs btn-primary">下载</a>
                                                            {if !$version.is_current}
                                                            <button type="button" class="btn btn-xs btn-success set-current-btn" data-id="{$version.id}">设为当前</button>
                                                            <button type="button" class="btn btn-xs btn-danger delete-version-btn" data-id="{$version.id}">删除</button>
                                                            {/if}
                                                        </td>
                                                    </tr>
                                                    {/volist}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="stock_price_api_url" class="control-label col-xs-12 col-sm-2">{:__('股票价格接口')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input type="text" class="form-control" id="stock_price_api_url" name="row[stock_price_api_url]" value="{$row.stock_price_api_url|default=''}" />
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="exchange_rate_api_url" class="control-label col-xs-12 col-sm-2">{:__('汇率接口')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input type="text" class="form-control" id="exchange_rate_api_url" name="row[exchange_rate_api_url]" value="{$row.exchange_rate_api_url|default=''}" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="transfer_control" class="control-label col-xs-12 col-sm-2">{:__('转账控制')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <select class="form-control" id="transfer_control" name="row[transfer_control]">
                                    <option value="0" {$row.transfer_control=='0'?'selected':''}>全部</option>
                                    <option value="1" {$row.transfer_control=='1'?'selected':''}>同B端</option>
                                    <option value="2" {$row.transfer_control=='2'?'selected':''}>关闭</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="transfer_approval_switch" class="control-label col-xs-12 col-sm-2">{:__('转账审批开关')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <label class="radio-inline">
                                    <input type="radio" name="row[transfer_approval_switch]" value="1" {$row.transfer_approval_switch=='1'?'checked':''} /> 开启
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="row[transfer_approval_switch]" value="0" {$row.transfer_approval_switch=='0'?'checked':''} /> 关闭
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-success btn-embossed">{:__('确认')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('重置')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="tab-pane fade" id="third">
                <div class="widget-body no-padding">
                    <form id="third-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('system.config/third')}">
                        
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4 class="panel-title">短信配置（阿里云）</h4>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label for="sms_key" class="control-label col-xs-12 col-sm-2">KEY:</label>
                                    <div class="col-xs-12 col-sm-8">
                                        <input type="text" class="form-control" id="sms_key" name="third[sms][accessKeyId]" value="{$third.sms.accessKeyId|default=''}" />
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="sms_secret" class="control-label col-xs-12 col-sm-2">SECRET:</label>
                                    <div class="col-xs-12 col-sm-8">
                                        <input type="password" class="form-control" id="sms_secret" name="third[sms][accessKeySecret]" value="{$third.sms.accessKeySecret|default=''}" />
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="sms_sign" class="control-label col-xs-12 col-sm-2">签名:</label>
                                    <div class="col-xs-12 col-sm-8">
                                        <input type="text" class="form-control" id="sms_sign" name="third[sms][signName]" value="{$third.sms.signName|default=''}" />
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="sms_default_template" class="control-label col-xs-12 col-sm-2">默认验证码模板:</label>
                                    <div class="col-xs-12 col-sm-8">
                                        <input type="text" class="form-control" id="sms_default_template" name="third[sms][templateCode]" value="{$third.sms.templateCode|default=''}" placeholder="默认短信验证码模板编号" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-primary btn-embossed">{:__('确认')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('重置')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="tab-pane fade" id="about">
                <div class="widget-body no-padding">
                    <form id="about-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('system.config/index')}">
                        <div class="form-group">
                            <label for="about_us" class="control-label col-xs-12 col-sm-2">{:__('关于我们内容')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea id="c-about-us" class="form-control editor" name="row[about_us]" rows="15">{$row.about_us|htmlentities|default=''}</textarea>
                            </div>
                        </div>
                        
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-success btn-embossed">{:__('确认')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('重置')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <!-- 新增注销风险提示Tab内容 -->
            <div class="tab-pane fade" id="delete_risk">
                <div class="widget-body no-padding">
                    <form id="delete-risk-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('system.config/index')}">
                        <div class="form-group">
                            <label for="delete_risk_tips" class="control-label col-xs-12 col-sm-2">{:__('注销风险提示内容')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea id="c-delete-risk-tips" class="form-control editor" name="row[delete_risk_tips]" rows="15">{$row.delete_risk_tips|htmlentities|default=''}</textarea>
                            </div>
                        </div>
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-success btn-embossed">{:__('确认')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('重置')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 新增隐私政策Tab内容 -->
            <div class="tab-pane fade" id="privacy_policy">
                <div class="widget-body no-padding">
                    <form id="privacy-policy-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('system.config/index')}">
                        <div class="form-group">
                            <label for="privacy_policy" class="control-label col-xs-12 col-sm-2">{:__('隐私政策内容')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea id="c-privacy-policy" class="form-control editor" name="row[privacy_policy]" rows="15">{$row.privacy_policy|htmlentities|default=''}</textarea>
                            </div>
                        </div>
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-success btn-embossed">{:__('确认')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('重置')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 新增用户协议Tab内容 -->
            <div class="tab-pane fade" id="user_agreement">
                <div class="widget-body no-padding">
                    <form id="user-agreement-form" class="edit-form form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('system.config/index')}">
                        <div class="form-group">
                            <label for="user_agreement" class="control-label col-xs-12 col-sm-2">{:__('用户协议内容')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea id="c-user-agreement" class="form-control editor" name="row[user_agreement]" rows="15">{$row.user_agreement|htmlentities|default=''}</textarea>
                            </div>
                        </div>
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-success btn-embossed">{:__('确认')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('重置')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- APP版本上传模态框 -->
<div class="modal fade" id="app-version-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">上传APP版本</h4>
            </div>
            <form id="app-version-form" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="modal-app-type" name="app_type" value="">

                    <div class="form-group">
                        <label for="modal-version-name">版本名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="modal-version-name" name="version_name" placeholder="例如：1.0.0" required>
                        <small class="help-block">版本名称，用于显示给用户</small>
                    </div>

                    <div class="form-group">
                        <label for="modal-version-code">版本号 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="modal-version-code" name="version_code" placeholder="例如：100" min="1" required>
                        <small class="help-block">版本号，用于版本比较，必须为正整数且递增</small>
                    </div>

                    <div class="form-group">
                        <label for="modal-app-file">APP文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="modal-app-file" name="file" required>
                        <small class="help-block">请选择APK或IPA文件</small>
                    </div>

                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="modal-is-force-update" name="is_force_update" value="1">
                            强制更新
                        </label>
                        <small class="help-block">勾选后，用户必须更新到此版本才能继续使用</small>
                    </div>

                    <div class="form-group">
                        <label for="modal-update-content">更新内容</label>
                        <textarea class="form-control" id="modal-update-content" name="update_content" rows="4" placeholder="请输入本次更新的内容描述..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary" id="upload-version-btn">
                        <i class="fa fa-upload"></i> 上传
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>