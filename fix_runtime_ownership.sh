#!/bin/bash

echo "修复runtime目录文件所有权脚本"
echo "================================"

# 确定web用户
if id "www" &>/dev/null; then
    WEB_USER="www"
elif id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    echo "未找到web用户，使用www"
    WEB_USER="www"
fi

echo "Web用户: $WEB_USER"

# 修复runtime目录下所有文件的所有权
echo "修复runtime目录文件所有权..."
chown -R $WEB_USER:$WEB_USER runtime/

# 确保目录权限正确
chmod -R 755 runtime/
chmod -R 777 runtime/cache/ 2>/dev/null || true
chmod -R 777 runtime/log/ 2>/dev/null || true
chmod -R 777 runtime/logs/ 2>/dev/null || true

echo "✅ runtime目录文件所有权修复完成"
echo "所有文件现在属于: $WEB_USER:$WEB_USER"
