<?php

namespace app\admin\controller\system;

use app\common\controller\Backend;
use app\common\model\EsopSystemConfig;
use app\common\model\EsopThirdPartyConfig;
use app\common\model\EsopAppVersion;
use think\Db;
use think\Exception;

/**
 * 系统基础配置管理
 * 
 * @icon fa fa-cogs
 * @remark 系统基础配置，包括公司信息、LOGO、第三方接口等
 */
class Config extends Backend
{
    // 无需权限的方法
    protected $noNeedRight = ['index', 'uploadLogo', 'third', 'uploadApp', 'uploadAppVersion', 'getAppVersions', 'setCurrentVersion', 'deleteAppVersion'];
    
    // 系统配置模型
    protected $model = null;
    
    /**
     * 初始化
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopSystemConfig();
        
        // 确保JS配置正确加载，使用正确的路径
        $this->assignconfig('jsname', 'backend/system/config');
        
       
        // 不需要额外设置布局，使用默认布局
    }
    
    /**
     * 查看和更新系统配置
     */
    public function index()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }
            
            // 处理LOGO上传
            $logo = $this->request->file('logo');
            if ($logo) {
                $logoUrl = EsopSystemConfig::uploadLogo($logo);
                if ($logoUrl) {
                    $params['logo_url'] = $logoUrl;
                }
            }
            
            // 更新配置
            $result = EsopSystemConfig::updateConfig($params);
            if ($result) {
                $this->success('更新成功');
            } else {
                $this->error('更新失败');
            }
        }
        
        // 获取当前配置
        $config = EsopSystemConfig::getConfig();
        $this->view->assign("row", $config);
        
        // 获取第三方配置
        $third = $this->getThirdPartyConfigs();
        $this->view->assign('third', $third);

        // 获取APP版本信息
        $androidVersions = EsopAppVersion::getVersionList('android', 5);
        $iosVersions = EsopAppVersion::getVersionList('ios', 5);
        $this->view->assign('androidVersions', $androidVersions);
        $this->view->assign('iosVersions', $iosVersions);

        return $this->view->fetch();
    }
    
    /**
     * 上传LOGO
     */
    public function uploadLogo()
    {
        // 获取表单上传文件
        $file = $this->request->file('file');
        if (!$file) {
            return json(['code' => 0, 'msg' => '没有文件上传']);
        }
        
        $logoUrl = EsopSystemConfig::uploadLogo($file);
        if ($logoUrl) {
            return json(['code' => 1, 'msg' => '上传成功', 'url' => $logoUrl]);
        } else {
            return json(['code' => 0, 'msg' => '上传失败']);
        }
    }

    /**
     * 上传APP包
     */
    public function uploadApp()
    {
        // 获取表单上传文件
        $file = $this->request->file('file');
        $appType = $this->request->post('app_type', ''); // android 或 ios

        if (!$file) {
            return json(['code' => 0, 'msg' => '没有文件上传']);
        }

        if (!in_array($appType, ['android', 'ios'])) {
            return json(['code' => 0, 'msg' => '应用类型参数错误']);
        }

        $appUrl = $this->uploadAppFile($file, $appType);
        if ($appUrl) {
            return json(['code' => 1, 'msg' => '上传成功', 'url' => $appUrl]);
        } else {
            return json(['code' => 0, 'msg' => '上传失败']);
        }
    }
    
    /**
     * 处理第三方配置
     */
    public function third()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("third/a");
            
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }
            
            try {
                Db::startTrans();
                
                // 处理SMS配置
                if (isset($params['sms'])) {
                    // 使用模型保存配置，自动处理时间戳
                    EsopThirdPartyConfig::saveConfigs($params['sms'], 'sms', 'aliyun');
                    
                    // 兼容旧数据 - 如果存在旧的templateCode，则更新为templateCode
                    $oldTemplate = EsopThirdPartyConfig::where([
                        'service_type' => 'sms',
                        'provider' => 'aliyun',
                        'config_key' => 'templateCode'
                    ])->find();
                    
                    if ($oldTemplate && !isset($params['sms']['templateCode'])) {
                        EsopThirdPartyConfig::create([
                            'service_type' => 'sms',
                            'provider' => 'aliyun',
                            'config_key' => 'templateCode',
                            'config_value' => $oldTemplate['config_value']
                        ]);
                    }
                }
                
                // 可以在这里处理其他第三方配置
                
                Db::commit();
                $this->success('更新成功');
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        
        return $this->error('请求方法错误');
    }
    
    /**
     * 获取第三方配置
     *
     * @return array
     */
    protected function getThirdPartyConfigs()
    {
        // 使用模型获取配置
        return EsopThirdPartyConfig::getConfigs();
    }

    /**
     * 上传APP文件
     *
     * @param object $file 上传文件对象
     * @param string $appType 应用类型 android|ios
     * @return string|false 成功返回文件路径，失败返回false
     */
    protected function uploadAppFile($file, $appType)
    {
        if (empty($file)) {
            return false;
        }

        // 根据应用类型设置允许的文件扩展名
        $allowedExtensions = [];
        if ($appType === 'android') {
            $allowedExtensions = ['apk'];
        } elseif ($appType === 'ios') {
            $allowedExtensions = ['ipa'];
        }

        // 获取文件后缀
        $suffix = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
        if (!in_array($suffix, $allowedExtensions)) {
            return false;
        }

        // 设置上传目录
        $uploadDir = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'apps' . DS . $appType;

        // 检查上传目录
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // 生成文件名
        $fileName = $appType . '_app_' . date('YmdHis') . rand(1000, 9999) . '.' . $suffix;

        // 保存文件
        $info = $file->move($uploadDir, $fileName);

        if ($info) {
            // 返回完整的文件访问URL（包含域名）
            $relativePath = '/uploads/apps/' . $appType . '/' . $fileName;
            $fullUrl = $this->getFullUrl($relativePath);
            return $fullUrl;
        } else {
            return false;
        }
    }

    /**
     * 获取完整URL（包含域名）
     *
     * @param string $relativePath 相对路径
     * @return string 完整URL
     */
    protected function getFullUrl($relativePath)
    {
        // 获取当前请求的协议
        $protocol = $this->request->isSsl() ? 'https://' : 'http://';

        // 获取当前请求的主机名
        $host = $this->request->host();

        // 组合完整URL
        $fullUrl = $protocol . $host . $relativePath;

        return $fullUrl;
    }

    /**
     * 按版本上传APP
     */
    public function uploadAppVersion()
    {
        // 获取表单上传文件
        $file = $this->request->file('file');
        $appType = $this->request->post('app_type', ''); // android 或 ios
        $versionName = $this->request->post('version_name', ''); // 版本名称，如：1.0.0
        $versionCode = $this->request->post('version_code', 0); // 版本号
        $isForceUpdate = $this->request->post('is_force_update', 0); // 是否强制更新
        $updateContent = $this->request->post('update_content', ''); // 更新内容

        if (!$file) {
            return json(['code' => 0, 'msg' => '没有文件上传']);
        }

        if (!in_array($appType, ['android', 'ios'])) {
            return json(['code' => 0, 'msg' => '应用类型参数错误']);
        }

        if (!$versionName) {
            return json(['code' => 0, 'msg' => '版本名称不能为空']);
        }

        if (!$versionCode || $versionCode <= 0) {
            return json(['code' => 0, 'msg' => '版本号必须为正整数']);
        }

        // 检查版本是否已存在
        $existVersion = EsopAppVersion::where('app_type', $appType)
            ->where('version_code', $versionCode)
            ->where('status', 'active')
            ->find();

        if ($existVersion) {
            return json(['code' => 0, 'msg' => '该版本号已存在']);
        }

        // 获取文件大小（在移动文件之前）
        $fileSize = $file->getSize();
        
        // 上传文件
        $appUrl = $this->uploadAppFile($file, $appType);
        if (!$appUrl) {
            return json(['code' => 0, 'msg' => '文件上传失败']);
        }

        // 创建版本记录
        $versionData = [
            'app_type' => $appType,
            'version_name' => $versionName,
            'version_code' => $versionCode,
            'file_path' => $appUrl,
            'file_url' => $appUrl,
            'file_size' => $fileSize,
            'is_current' => 1, // 新上传的版本默认设为当前版本
            'is_force_update' => $isForceUpdate ? 1 : 0,
            'update_content' => $updateContent,
            'status' => 'active'
        ];

        $version = EsopAppVersion::createVersion($versionData);
        if ($version) {
            return json(['code' => 1, 'msg' => '上传成功', 'data' => $version]);
        } else {
            return json(['code' => 0, 'msg' => '版本创建失败']);
        }
    }

    /**
     * 获取APP版本列表
     */
    public function getAppVersions()
    {
        $appType = $this->request->get('app_type', '');
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        if (!in_array($appType, ['android', 'ios'])) {
            return json(['code' => 0, 'msg' => '应用类型参数错误']);
        }

        $query = EsopAppVersion::where('app_type', $appType)
            ->where('status', 'active')
            ->order('version_code', 'desc');

        $total = $query->count();
        $versions = $query->page($page, $limit)->select();

        return json([
            'code' => 1,
            'msg' => '获取成功',
            'data' => [
                'list' => $versions,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }

    /**
     * 设置当前版本
     */
    public function setCurrentVersion()
    {
        $id = $this->request->post('id', 0);

        if (!$id) {
            return json(['code' => 0, 'msg' => '版本ID不能为空']);
        }

        $result = EsopAppVersion::setCurrent($id);
        if ($result) {
            return json(['code' => 1, 'msg' => '设置成功']);
        } else {
            return json(['code' => 0, 'msg' => '设置失败']);
        }
    }

    /**
     * 删除APP版本
     */
    public function deleteAppVersion()
    {
        $id = $this->request->post('id', 0);

        if (!$id) {
            return json(['code' => 0, 'msg' => '版本ID不能为空']);
        }

        $result = EsopAppVersion::deleteVersion($id);
        if ($result) {
            return json(['code' => 1, 'msg' => '删除成功']);
        } else {
            return json(['code' => 0, 'msg' => '删除失败，当前版本不能删除']);
        }
    }
}
