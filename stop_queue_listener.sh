#!/bin/bash

echo "停止队列监听服务脚本"
echo "================================"

# 检查PID文件是否存在
if [ -f "runtime/queue_pids.txt" ]; then
    echo "从PID文件读取进程ID..."
    PIDS=$(cat runtime/queue_pids.txt)
    echo "找到PID: $PIDS"
    
    # 停止进程
    for PID in $PIDS; do
        if kill -0 $PID 2>/dev/null; then
            echo "正在停止进程 $PID..."
            kill $PID
            sleep 2
            # 如果进程仍在运行，强制杀死
            if kill -0 $PID 2>/dev/null; then
                echo "强制停止进程 $PID..."
                kill -9 $PID
            fi
        else
            echo "进程 $PID 已经停止"
        fi
    done
    
    # 删除PID文件
    rm runtime/queue_pids.txt
    echo "PID文件已删除"
else
    echo "PID文件不存在，尝试查找队列进程..."
fi

# 查找并停止所有队列相关进程
echo "查找所有队列相关进程..."
QUEUE_PIDS=$(ps aux | grep -E "(php.*think.*queue|queue:listen)" | grep -v grep | awk '{print $2}')

if [ -n "$QUEUE_PIDS" ]; then
    echo "找到队列进程: $QUEUE_PIDS"
    for PID in $QUEUE_PIDS; do
        echo "停止进程 $PID..."
        kill $PID
        sleep 1
        # 检查是否还在运行
        if kill -0 $PID 2>/dev/null; then
            echo "强制停止进程 $PID..."
            kill -9 $PID
        fi
    done
else
    echo "没有找到运行中的队列进程"
fi

# 停止tmux会话（如果存在）
if command -v tmux &> /dev/null; then
    if tmux has-session -t queue_listeners 2>/dev/null; then
        echo "停止tmux会话 queue_listeners..."
        tmux kill-session -t queue_listeners
    fi
fi

echo "队列监听服务已停止!"
