<?php

namespace app\common\model;

use think\Model;

/**
 * UI风格模型
 */
class UiStyle extends Model
{
    // 表名
    protected $name = 'esop_ui_styles';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 追加属性
    protected $append = [
        'is_default_text'
    ];
    
    /**
     * 获取默认状态列表
     * @return array
     */
    public function getIsDefaultList()
    {
        return ['0' => __('否'), '1' => __('是')];
    }
    
    /**
     * 获取默认状态文本
     * @param  string $value  值
     * @param  array  $data   行数据
     * @return string
     */
    public function getIsDefaultTextAttr($value, $data)
    {
        $value = $value ? $value : $data['is_default'];
        $list = $this->getIsDefaultList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    /**
     * 设置默认风格
     * @param int $id 风格ID
     * @return bool
     */
    public static function setDefault($id)
    {
        // 先将所有风格设为非默认
        self::where('is_default', 1)->update(['is_default' => 0]);
        // 再将指定风格设为默认
        return self::where('id', $id)->update(['is_default' => 1]) > 0;
    }
    
    /**
     * 获取默认风格
     * @return UiStyle|null
     */
    public static function getDefault()
    {
        return self::where('is_default', 1)->find();
    }

    /**
     * 确保有默认风格，如果没有则设置第一个为默认
     * @return bool
     */
    public static function ensureDefault()
    {
        // 检查是否有默认风格
        $defaultStyle = self::getDefault();
        if ($defaultStyle) {
            return true;
        }

        // 如果没有默认风格，设置第一个为默认
        $firstStyle = self::order('id', 'asc')->find();
        if ($firstStyle) {
            return self::setDefault($firstStyle->id);
        }

        return false;
    }
}