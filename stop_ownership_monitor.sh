#!/bin/bash

echo "停止文件所有权监听服务"
echo "================================"

STOPPED=false

# 从PID文件停止
if [ -f "runtime/ownership_monitor.pid" ]; then
    PID=$(cat runtime/ownership_monitor.pid)
    echo "从PID文件读取到PID: $PID"
    if kill -0 $PID 2>/dev/null; then
        echo "停止监听服务 PID: $PID"
        kill $PID
        sleep 3
        if kill -0 $PID 2>/dev/null; then
            echo "正常停止失败，强制停止..."
            kill -9 $PID
        fi
        STOPPED=true
    else
        echo "PID $PID 进程不存在"
    fi
    rm runtime/ownership_monitor.pid
    echo "PID文件已删除"
fi

# 查找并停止所有相关进程
MONITOR_PIDS=$(pgrep -f "ownership_monitor.sh")
if [ -n "$MONITOR_PIDS" ]; then
    echo "发现运行中的监听进程: $MONITOR_PIDS"
    for PID in $MONITOR_PIDS; do
        echo "停止进程 $PID"
        kill $PID
        STOPPED=true
    done
    sleep 3

    # 检查是否还有进程
    REMAINING=$(pgrep -f "ownership_monitor.sh")
    if [ -n "$REMAINING" ]; then
        echo "强制停止剩余进程: $REMAINING"
        pkill -9 -f "ownership_monitor.sh"
        sleep 1
    fi
fi

if [ "$STOPPED" = true ]; then
    echo "✅ 监听服务已停止"
else
    echo "ℹ️  没有发现运行中的监听服务"
fi

# 最终检查
FINAL_CHECK=$(pgrep -f "ownership_monitor.sh")
if [ -n "$FINAL_CHECK" ]; then
    echo "⚠️  警告: 仍有进程在运行: $FINAL_CHECK"
else
    echo "✅ 确认所有监听进程已停止"
fi
