#!/bin/bash

echo "停止文件所有权监听服务"
echo "================================"

# 从PID文件停止
if [ -f "runtime/ownership_monitor.pid" ]; then
    PID=$(cat runtime/ownership_monitor.pid)
    if kill -0 $PID 2>/dev/null; then
        echo "停止监听服务 PID: $PID"
        kill $PID
        sleep 2
        if kill -0 $PID 2>/dev/null; then
            echo "强制停止..."
            kill -9 $PID
        fi
    else
        echo "PID $PID 进程不存在"
    fi
    rm runtime/ownership_monitor.pid
fi

# 查找并停止所有相关进程
MONITOR_PIDS=$(pgrep -f "ownership_monitor.sh")
if [ -n "$MONITOR_PIDS" ]; then
    echo "停止所有监听进程: $MONITOR_PIDS"
    for PID in $MONITOR_PIDS; do
        kill $PID
    done
    sleep 2
    # 检查是否还有进程
    REMAINING=$(pgrep -f "ownership_monitor.sh")
    if [ -n "$REMAINING" ]; then
        echo "强制停止剩余进程: $REMAINING"
        pkill -9 -f "ownership_monitor.sh"
    fi
fi

echo "监听服务已停止"
