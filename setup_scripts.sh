#!/bin/bash

echo "设置队列管理脚本"
echo "================================"

# 设置所有脚本的执行权限
chmod +x stop_queue_listener.sh
chmod +x start_queue_as_www.sh
chmod +x deploy_as_www.sh
chmod +x fix_permissions.sh
chmod +x check_and_prevent_root_issues.sh
chmod +x monitor_queue_permissions.sh 2>/dev/null || true

echo "✅ 所有脚本权限已设置"
echo ""
echo "可用的脚本:"
echo "1. ./check_and_prevent_root_issues.sh - 全面检查和修复权限问题"
echo "2. ./fix_permissions.sh - 快速修复权限"
echo "3. ./start_queue_as_www.sh - 以www用户启动队列"
echo "4. ./stop_queue_listener.sh - 停止队列"
echo "5. ./deploy_as_www.sh - 完整部署脚本"
echo ""
echo "建议首先运行: ./check_and_prevent_root_issues.sh"
