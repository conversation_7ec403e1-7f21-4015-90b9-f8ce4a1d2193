<?php

namespace app\admin\controller\uistyle;

use app\common\controller\Backend;
use app\common\model\UiStyle as UiStyleModel;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * UI风格管理
 * 
 * @icon fa fa-paint-brush
 */
class Uistyle extends Backend
{
    /**
     * UiStyle模型对象
     * @var \app\common\model\UiStyle
     */
    protected $model = null;
    
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new UiStyleModel;
        $this->view->assign("isDefaultList", $this->model->getIsDefaultList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    
    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                
                // 检查是否设为默认
                $isDefault = isset($params['is_default']) && $params['is_default'] == 1;
                
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    
                    // 如果设为默认，需要更新其他风格为非默认
                    if ($isDefault && $result) {
                        UiStyleModel::setDefault($this->model->id);
                    }
                    
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 检查是否设为默认
                $isDefault = isset($params['is_default']) && $params['is_default'] == 1;
                // 检查是否将当前默认样式改为非默认
                $isCurrentDefault = $row->is_default == 1;
                $isChangingToNonDefault = isset($params['is_default']) && $params['is_default'] == 0 && $isCurrentDefault;

                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }

                    // 如果要将当前默认样式改为非默认，需要检查是否还有其他样式
                    if ($isChangingToNonDefault) {
                        $otherStylesCount = UiStyleModel::where('id', '<>', $ids)->count();
                        if ($otherStylesCount == 0) {
                            throw new Exception('必须保留至少一个默认样式');
                        }
                    }

                    $result = $row->allowField(true)->save($params);

                    // 如果设为默认，需要更新其他风格为非默认
                    if ($isDefault && $result) {
                        UiStyleModel::setDefault($ids);
                    }
                    // 如果将当前默认样式改为非默认，需要自动设置第一个样式为默认
                    elseif ($isChangingToNonDefault && $result) {
                        $firstStyle = UiStyleModel::where('id', '<>', $ids)->order('id', 'asc')->find();
                        if ($firstStyle) {
                            UiStyleModel::setDefault($firstStyle->id);
                        }
                    }

                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
    
    /**
     * 设为默认
     */
    public function setdefault($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $result = UiStyleModel::setDefault($ids);
        if ($result) {
            $this->success();
        } else {
            $this->error(__('Operation failed'));
        }
    }

    /**
     * 删除
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        // 检查是否要删除默认样式
        $hasDefault = false;
        $totalCount = UiStyleModel::count();
        $deleteCount = count($list);

        foreach ($list as $item) {
            if ($item->is_default == 1) {
                $hasDefault = true;
                break;
            }
        }

        // 如果要删除默认样式，且删除后还有其他样式，则需要重新设置默认样式
        // 如果删除后没有任何样式了，则不允许删除
        if ($deleteCount >= $totalCount) {
            $this->error('不能删除所有样式，必须保留至少一个样式');
        }

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }

            // 如果删除了默认样式，需要重新设置默认样式
            if ($hasDefault) {
                UiStyleModel::ensureDefault();
            }

            Db::commit();
        } catch (\think\exception\PDOException $e) {
            Db::rollback();
            $this->error($e->getMessage());
        } catch (\think\Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }
}