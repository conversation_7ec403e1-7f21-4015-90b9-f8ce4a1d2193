define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 财务管理首页，不再需要绘制趋势图
        },
        grantassets: function () {
            // 在页面头部添加统计信息
            $("#myTabContent").prepend($("#finance-assets-statistics").html());
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'finance/finance/grantassets',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true, align: 'center', width: '5%'},
                        {field: 'member_name', title: __('会员/商家'), align: 'left', width: '15%'},
                        {field: 'operator_name', title: __('操作人'), align: 'left', width: '10%'},
                        {field: 'vesting_rule_name', title: __('解禁规则名称'), align: 'left', width: '12%'},
                        {field: 'formatted_amount', title: __('变动数量'), align: 'right', operate: false, width: '10%', formatter: function(value, row, index) {
                            return value;
                        }},
                        {field: 'after_amount', title: __('变动后代解禁资产'), align: 'right', operate: false, width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            if (!isNaN(num)) {
                                return num.toFixed(2);
                            }
                            return value || '-';
                        }},
                        {field: 'remark', title: __('描述/说明'), align: 'left', operate: false, width: '18%'},
                        {field: 'formatted_time', title: __('变动时间'), align: 'center', operate: 'RANGE', addclass: 'datetimerange', width: '15%'},
                    ]
                ],
                // 启用行点击选中
                clickToSelect: true,
                // 启用显示隐藏列
                showColumns: true,
                // 启用排序
                sortable: true,
                // 启用显示列脱敏
                escape: false,
                // 显示导出按钮
                showExport: true,
                exportDataType: "all",
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 搜索按钮点击事件
            $("#btn-search").on("click", function () {
                var params = {};
                if ($("#search-member").val()) {
                    params.filter = JSON.stringify({member: $("#search-member").val()});
                }
                table.bootstrapTable('refresh', {query: params});
            });
            
            // 回车搜索
            $("#search-member").keydown(function (e) {
                if (e.keyCode == 13) {
                    $("#btn-search").trigger("click");
                }
            });
            
            // 导出按钮点击事件
            $("#btn-export-file").on("click", function () {
                var options = table.bootstrapTable('getOptions');
                var columns = [];
                $.each(options.columns[0], function (i, j) {
                    if (j.field && !j.checkbox && j.visible && j.field != 'operate') {
                        columns.push({
                            field: j.field,
                            title: j.title
                        });
                    }
                });
                var dataUrl = options.url;
                if ($("#search-member").val()) {
                    dataUrl += "?filter=" + encodeURIComponent(JSON.stringify({member: $("#search-member").val()}));
                }
                
                Layer.confirm('确认导出授权资产记录?', {icon: 3, title: __('提示')}, function (index) {
                    Fast.api.open(dataUrl + (dataUrl.indexOf("?") > -1 ? "&" : "?") + "export=1&callback=", __('导出'), {
                        callback: function (data) {
                            if (data) {
                                table.bootstrapTable('refresh');
                            }
                        }
                    });
                    Layer.close(index);
                });
            });
        },
        pendingassets: function () {
            // 在页面头部添加统计信息
            $("#myTabContent").prepend($("#finance-assets-statistics").html());
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'finance/finance/pendingAssets',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'user_id',
                sortName: 'user_id',
                sortOrder: 'desc',
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                columns: [
                    [
                        {checkbox: true},
                        {field: 'user_id', title: __('会员ID'), sortable: true, align: 'center', width: '12%'},
                        {field: 'member_name', title: __('会员'), align: 'left', width: '20%'},
                        {field: 'pending_amount', title: __('待解禁数量'), align: 'right', operate: false, width: '15%', formatter: function(value, row, index) {
                            return '<span style="color: #ff6b6b; font-weight: bold;">' + value + '</span>';
                        }},
                        {field: 'grant_amount', title: __('授权总数量'), align: 'right', operate: false, width: '15%', formatter: function(value, row, index) {
                            return '<span style="color: #4ecdc4;">' + value + '</span>';
                        }},
                        {field: 'next_vesting_date', title: __('下次解禁日期'), align: 'center', operate: 'RANGE', addclass: 'daterange', width: '15%'},
                        {field: 'next_vesting_amount', title: __('下次解禁金额'), align: 'right', operate: false, width: '15%', formatter: function(value, row, index) {
                            return '<span style="color: #f39c12;">' + value + '</span>';
                        }},
                    ]
                ],
                // 启用行点击选中
                clickToSelect: true,
                // 启用显示隐藏列
                showColumns: true,
                // 启用排序
                sortable: true,
                // 启用显示列脱敏
                escape: false,
                // 显示导出按钮
                showExport: true,
                exportDataType: "all",
                // 定义工具栏
                toolbar: "#toolbar",
                // 提示信息
                search: false,
                showRefresh: false,
                showToggle: false,
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 绑定刷新按钮事件
            $('.btn-refresh').on('click', function () {
                table.bootstrapTable('refresh');
            });
            
            // 导出按钮点击事件
            $("#btn-export-file").on("click", function () {
                var options = table.bootstrapTable('getOptions');
                var columns = [];
                $.each(options.columns[0], function (i, j) {
                    if (j.field && !j.checkbox && j.visible && j.field != 'operate') {
                        columns.push({
                            field: j.field,
                            title: j.title
                        });
                    }
                });
                
                Layer.confirm('确认导出待解禁记录?', {icon: 3, title: __('提示')}, function (index) {
                    Fast.api.open(options.url + (options.url.indexOf("?") > -1 ? "&" : "?") + "export=1&callback=", __('导出'), {
                        callback: function (data) {
                            if (data) {
                                table.bootstrapTable('refresh');
                            }
                        }
                    });
                    Layer.close(index);
                });
            });
        },
        vestedassets: function () {
            // 在页面头部添加统计信息
            $("#myTabContent").prepend($("#finance-assets-statistics").html());
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'finance/finance/vestedAssets',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'vesting_amount',
                sortOrder: 'desc',
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                columns: [
                    [
                        {checkbox: true},
                        {field: 'user_id', title: __('会员ID'), sortable: true, align: 'center', width: '8%'},
                        {field: 'member_name', title: __('会员'), align: 'left', width: '15%'},
                        {field: 'vesting_amount', title: __('解禁总数量'), align: 'right', operate: false, width: '12%', formatter: function(value, row, index) {
                            return '<span style="color: #28a745; font-weight: bold;">' + value + '</span>';
                        }},
                        {field: 'grant_amount', title: __('授权总数量'), align: 'right', operate: false, width: '12%', formatter: function(value, row, index) {
                            return '<span style="color: #4ecdc4;">' + value + '</span>';
                        }},
                        {field: 'vesting_ratio', title: __('解禁比例'), align: 'right', operate: false, width: '10%', formatter: function(value, row, index) {
                            return '<span style="color: #007bff;">' + value + '</span>';
                        }},
                        {field: 'vesting_count', title: __('解禁次数'), align: 'center', operate: false, width: '10%', formatter: function(value, row, index) {
                            return '<span style="color: #6c757d;">' + value + '</span>';
                        }},
                        {field: 'vesting_date', title: __('最近解禁日期'), align: 'center', operate: 'RANGE', addclass: 'daterange', width: '15%'},
                    ]
                ],
                // 启用行点击选中
                clickToSelect: true,
                // 启用显示隐藏列
                showColumns: true,
                // 启用排序
                sortable: true,
                // 启用显示列脱敏
                escape: false,
                // 显示导出按钮
                showExport: true,
                exportDataType: "all",
                // 定义工具栏
                toolbar: "#toolbar",
                // 提示信息
                search: false,
                showRefresh: false,
                showToggle: false,
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 绑定刷新按钮事件
            $('.btn-refresh').on('click', function () {
                table.bootstrapTable('refresh');
            });
            
            // 导出按钮点击事件
            $("#btn-export-file").on("click", function () {
                var options = table.bootstrapTable('getOptions');
                var columns = [];
                $.each(options.columns[0], function (i, j) {
                    if (j.field && !j.checkbox && j.visible && j.field != 'operate') {
                        columns.push({
                            field: j.field,
                            title: j.title
                        });
                    }
                });
                
                Layer.confirm('确认导出已解禁记录?', {icon: 3, title: __('提示')}, function (index) {
                    Fast.api.open(options.url + (options.url.indexOf("?") > -1 ? "&" : "?") + "export=1&callback=", __('导出'), {
                        callback: function (data) {
                            if (data) {
                                table.bootstrapTable('refresh');
                            }
                        }
                    });
                    Layer.close(index);
                });
            });
        },
        exchangedassets: function () {
            // 在页面头部添加统计信息
            $("#myTabContent").prepend($("#finance-assets-statistics").html());
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'finance/finance/exchangedAssets',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                columns: [
                    [
                        {checkbox: true},
                        {field: 'user_id', title: __('用户ID'), sortable: true, align: 'center', width: '8%'},
                        {field: 'member_name', title: __('用户名称'), align: 'left', width: '12%'},
                        {field: 'exchange_count', title: __('换股次数'), align: 'center', width: '10%'},
                        {field: 'total_exchange_amount', title: __('总换股数量'), align: 'right', width: '12%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(4) : (value || '-');
                        }},
                        {field: 'total_exchange_value', title: __('总换股金额'), align: 'right', width: '12%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(4) : (value || '-');
                        }},
                        {field: 'stock_details', title: __('持股详情'), align: 'left', width: '20%'},
                        {field: 'last_exchange_time', title: __('最后换股时间'), align: 'center', operate: 'RANGE', addclass: 'datetimerange', width: '15%', formatter: Table.api.formatter.datetime},
                    ]
                ],
                // 启用行点击选中
                clickToSelect: true,
                // 启用显示隐藏列
                showColumns: true,
                // 启用排序
                sortable: true,
                // 启用显示列脱敏
                escape: false,
                // 显示导出按钮
                showExport: true,
                exportDataType: "all",
                // 定义工具栏
                toolbar: "#toolbar",
                // 提示信息
                search: false,
                showRefresh: false,
                showToggle: false,
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 搜索按钮点击事件
            $("#btn-search").on("click", function () {
                var params = {};
                if ($("#search-member").val()) {
                    params.filter = JSON.stringify({member: $("#search-member").val()});
                }
                if ($("#search-date-range").val()) {
                    var dateRange = $("#search-date-range").val().split(' - ');
                    if (dateRange.length == 2) {
                        if (!params.filter) {
                            params.filter = JSON.stringify({created_at: [dateRange[0] + ' 00:00:00', dateRange[1] + ' 23:59:59']});
                        } else {
                            var filter = JSON.parse(params.filter);
                            filter.created_at = [dateRange[0] + ' 00:00:00', dateRange[1] + ' 23:59:59'];
                            params.filter = JSON.stringify(filter);
                        }
                    }
                }
                table.bootstrapTable('refresh', {query: params});
            });
            
            // 重置按钮点击事件
            $("#btn-reset").on("click", function () {
                $("#search-member").val('');
                $("#search-date-range").val('');
                table.bootstrapTable('refresh', {query: {}});
            });
            
            // 绑定刷新按钮事件
            $('.btn-refresh').on('click', function () {
                table.bootstrapTable('refresh');
            });
            
            // 导出按钮点击事件
            $("#btn-export-file").on("click", function () {
                var options = table.bootstrapTable('getOptions');
                var columns = [];
                $.each(options.columns[0], function (i, j) {
                    if (j.field && !j.checkbox && j.visible && j.field != 'operate') {
                        columns.push({
                            field: j.field,
                            title: j.title
                        });
                    }
                });
                
                var params = {};
                if ($("#search-member").val()) {
                    params.filter = JSON.stringify({member: $("#search-member").val()});
                }
                if ($("#search-date-range").val()) {
                    var dateRange = $("#search-date-range").val().split(' - ');
                    if (dateRange.length == 2) {
                        if (!params.filter) {
                            params.filter = JSON.stringify({created_at: [dateRange[0] + ' 00:00:00', dateRange[1] + ' 23:59:59']});
                        } else {
                            var filter = JSON.parse(params.filter);
                            filter.created_at = [dateRange[0] + ' 00:00:00', dateRange[1] + ' 23:59:59'];
                            params.filter = JSON.stringify(filter);
                        }
                    }
                }
                
                var dataUrl = options.url;
                if (Object.keys(params).length > 0) {
                    var queryString = $.param(params);
                    dataUrl += (dataUrl.indexOf("?") > -1 ? "&" : "?") + queryString;
                }
                
                Layer.confirm('确认导出已换股记录?', {icon: 3, title: __('提示')}, function (index) {
                    Fast.api.open(dataUrl + (dataUrl.indexOf("?") > -1 ? "&" : "?") + "export=1&callback=", __('导出'), {
                        callback: function (data) {
                            if (data) {
                                table.bootstrapTable('refresh');
                            }
                        }
                    });
                    Layer.close(index);
                });
            });
        },
        exerciseassets: function () {
            // 在页面头部添加统计信息
            $("#myTabContent").prepend($("#finance-assets-statistics").html());
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'finance/finance/exerciseAssets',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                columns: [
                    [
                        {checkbox: true},
                        {field: 'user_id', title: __('用户ID'), sortable: true, align: 'center', width: '8%'},
                        {field: 'member_name', title: __('用户名称'), align: 'left', width: '12%'},
                        {field: 'exercise_count', title: __('行权次数'), align: 'center', width: '10%'},
                        {field: 'total_exercise_amount', title: __('总行权数量'), align: 'right', width: '12%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(4) : (value || '-');
                        }},
                        {field: 'total_exercise_value', title: __('总行权金额'), align: 'right', width: '12%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(4) : (value || '-');
                        }},
                        {field: 'total_exercise_fee', title: __('总手续费'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(4) : (value || '-');
                        }},
                        {field: 'approval_status_summary', title: __('审批状态汇总'), align: 'center', width: '15%', formatter: function(value, row, index) {
                            return value;
                        }},
                        {field: 'exercise_details', title: __('行权详情'), align: 'left', width: '18%'},
                        {field: 'last_exercise_time', title: __('最后行权时间'), align: 'center', width: '15%', operate: 'RANGE', addclass: 'datetimerange', formatter: Table.api.formatter.datetime},
                    ]
                ],
                // 启用行点击选中
                clickToSelect: true,
                // 启用显示隐藏列
                showColumns: true,
                // 启用排序
                sortable: true,
                // 启用显示列脱敏
                escape: false,
                // 显示导出按钮
                showExport: true,
                exportDataType: "all",
                // 定义工具栏
                toolbar: "#toolbar",
                // 提示信息
                search: false,
                showRefresh: false,
                showToggle: false,
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 绑定刷新按钮事件
            $('.btn-refresh').on('click', function () {
                table.bootstrapTable('refresh');
            });
            
            // 导出按钮点击事件
            $("#btn-export-file").on("click", function () {
                var options = table.bootstrapTable('getOptions');
                var columns = [];
                $.each(options.columns[0], function (i, j) {
                    if (j.field && !j.checkbox && j.visible && j.field != 'operate') {
                        columns.push({
                            field: j.field,
                            title: j.title
                        });
                    }
                });
                
                Layer.confirm('确认导出行权记录?', {icon: 3, title: __('提示')}, function (index) {
                    Fast.api.open(options.url + (options.url.indexOf("?") > -1 ? "&" : "?") + "export=1&callback=", __('导出'), {
                        callback: function (data) {
                            if (data) {
                                table.bootstrapTable('refresh');
                            }
                        }
                    });
                    Layer.close(index);
                });
            });
        },
        assetlogs: function () {
            // 在页面头部添加统计信息
            $("#myTabContent").prepend($("#finance-assets-statistics").html());
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'finance/finance/assetLogs',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                pageSize: 10,
                pageList: [10, 20, 50, 100],
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true, align: 'center', width: '5%'},
                        {field: 'user_name', title: __('用户名称'), align: 'left', width: '10%'},
                        {field: 'formatted_amount', title: __('变动金额'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            return value;
                        }},
                        {field: 'action_type_text', title: __('操作类型'), align: 'center', width: '8%'},
                        {field: 'before_available_assets', title: __('操作前可用资产'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(2) : (value || '-');
                        }},
                        {field: 'after_available_assets', title: __('操作后可用资产'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(2) : (value || '-');
                        }},
                        {field: 'before_pending_assets', title: __('操作前待解冻资产'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(2) : (value || '-');
                        }},
                        {field: 'after_pending_assets', title: __('操作后待解冻资产'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(2) : (value || '-');
                        }},
                        {field: 'before_total_assets', title: __('操作前总资产'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(2) : (value || '-');
                        }},
                        {field: 'after_total_assets', title: __('操作后总资产'), align: 'right', width: '10%', formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return !isNaN(num) ? num.toFixed(2) : (value || '-');
                        }},
                        {field: 'remark', title: __('备注'), align: 'left', width: '10%'},
                        {field: 'formatted_time', title: __('操作时间'), align: 'center', width: '15%'},
                    ]
                ],
                // 启用行点击选中
                clickToSelect: true,
                // 启用显示隐藏列
                showColumns: true,
                // 启用排序
                sortable: true,
                // 启用显示列脱敏
                escape: false,
                // 显示导出按钮
                showExport: true,
                exportDataType: "all",
                // 定义工具栏
                toolbar: "#toolbar",
                // 提示信息
                search: false,
                showRefresh: false,
                showToggle: false,
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 绑定刷新按钮事件
            $('.btn-refresh').on('click', function () {
                table.bootstrapTable('refresh');
            });
            
            // 导出按钮点击事件
            $("#btn-export-file").on("click", function () {
                var options = table.bootstrapTable('getOptions');
                var columns = [];
                $.each(options.columns[0], function (i, j) {
                    if (j.field && !j.checkbox && j.visible && j.field != 'operate') {
                        columns.push({
                            field: j.field,
                            title: j.title
                        });
                    }
                });
                
                Layer.confirm('确认导出用户资产记录?', {icon: 3, title: __('提示')}, function (index) {
                    Fast.api.open(options.url + (options.url.indexOf("?") > -1 ? "&" : "?") + "export=1&callback=", __('导出'), {
                        callback: function (data) {
                            if (data) {
                                table.bootstrapTable('refresh');
                            }
                        }
                    });
                    Layer.close(index);
                });
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});