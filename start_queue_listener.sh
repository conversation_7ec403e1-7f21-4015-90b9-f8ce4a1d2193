#!/bin/bash

echo "解禁队列监听服务启动脚本"
echo "================================"

# 检查是否有参数
if [ "$1" == "--foreground" ] || [ "$1" == "-f" ]; then
    # 前台运行模式
    echo "在前台模式启动队列监听（按Ctrl+C可停止）..."
    echo "正在启动默认队列..."
    # 使用tmux创建分屏会话，这样可以同时显示两个队列的输出
    if command -v tmux &> /dev/null; then
        echo "使用tmux创建分屏会话..."
        # 创建新会话并启动第一个队列
        tmux new-session -d -s queue_listeners "php think queue:listen --queue default --verbose"
        # 在同一个窗口分屏并启动第二个队列
        tmux split-window -v "php think queue:listen --queue vesting --verbose"
        # 连接到会话
        tmux -2 attach-session -t queue_listeners
    else
        echo "tmux未安装，在单一控制台运行vesting队列（无法同时查看两个队列）"
        # 如果没有tmux，直接在当前控制台运行解禁队列
        php think queue:listen --queue vesting --verbose
    fi
else
    # 后台运行模式
    echo "在后台模式启动队列监听服务..."
    
    # 创建日志目录（如果不存在）
    mkdir -p runtime/logs/queue
    
    # 启动默认队列监听
    nohup php think queue:listen --queue default > runtime/logs/queue/default_$(date +%Y%m%d).log 2>&1 &
    DEFAULT_PID=$!
    echo "默认队列已启动，PID: $DEFAULT_PID"
    
    # 启动解禁任务队列监听
    nohup php think queue:listen --queue vesting > runtime/logs/queue/vesting_$(date +%Y%m%d).log 2>&1 &
    VESTING_PID=$!
    echo "解禁任务队列已启动，PID: $VESTING_PID"
    
    echo "队列监听服务已在后台启动!"
    echo "查看日志: runtime/logs/queue/default_$(date +%Y%m%d).log 和 runtime/logs/queue/vesting_$(date +%Y%m%d).log" 
    echo "要停止服务，请运行: kill $DEFAULT_PID $VESTING_PID"
    
    # 保存PID到文件以便后续停止
    echo "$DEFAULT_PID $VESTING_PID" > runtime/queue_pids.txt
fi 