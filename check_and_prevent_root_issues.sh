#!/bin/bash

echo "检查和预防root用户权限问题脚本"
echo "================================"

PROJECT_DIR=$(pwd)

# 确定web用户
if id "www" &>/dev/null; then
    WEB_USER="www"
elif id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    echo "未找到合适的web用户，将创建www用户"
    sudo useradd -r -s /bin/bash -d /var/www www
    WEB_USER="www"
fi

echo "使用web用户: $WEB_USER"

# 1. 检查当前权限问题
echo ""
echo "1. 检查当前权限问题..."
echo "================================"

# 检查runtime目录下的root文件
ROOT_FILES=$(find "$PROJECT_DIR/runtime" -user root 2>/dev/null)
ROOT_COUNT=$(echo "$ROOT_FILES" | grep -v '^$' | wc -l)

if [ "$ROOT_COUNT" -gt 0 ]; then
    echo "❌ 发现 $ROOT_COUNT 个root用户创建的文件/目录:"
    echo "$ROOT_FILES" | head -10
    if [ "$ROOT_COUNT" -gt 10 ]; then
        echo "... 还有 $((ROOT_COUNT - 10)) 个文件"
    fi
    
    echo ""
    echo "这些文件会导致以下问题:"
    echo "- Web服务器无法写入缓存文件"
    echo "- 日志文件无法创建或更新"
    echo "- 队列任务可能失败"
    echo "- 文件上传可能失败"
    
    read -p "是否立即修复这些权限问题? (y/n): " fix_now
    if [[ $fix_now =~ ^[Yy]$ ]]; then
        echo "正在修复权限..."
        sudo chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR/runtime"
        sudo chmod -R 777 "$PROJECT_DIR/runtime"
        echo "✅ 权限修复完成"
    fi
else
    echo "✅ 未发现root用户权限问题"
fi

# 2. 检查当前运行的进程
echo ""
echo "2. 检查当前运行的队列进程..."
echo "================================"

QUEUE_PROCESSES=$(ps aux | grep -E "(php.*think.*queue|queue:listen)" | grep -v grep)
if [ -n "$QUEUE_PROCESSES" ]; then
    echo "当前运行的队列进程:"
    echo "$QUEUE_PROCESSES"
    
    # 检查是否有root用户运行的队列
    ROOT_QUEUES=$(echo "$QUEUE_PROCESSES" | grep "^root")
    if [ -n "$ROOT_QUEUES" ]; then
        echo ""
        echo "❌ 警告: 发现以root用户运行的队列进程:"
        echo "$ROOT_QUEUES"
        echo ""
        echo "建议立即停止这些进程并以$WEB_USER用户重新启动"
        
        read -p "是否停止root用户的队列进程? (y/n): " stop_root
        if [[ $stop_root =~ ^[Yy]$ ]]; then
            echo "停止root用户的队列进程..."
            echo "$ROOT_QUEUES" | awk '{print $2}' | xargs sudo kill
            echo "✅ root队列进程已停止"
            
            read -p "是否以$WEB_USER用户重新启动队列? (y/n): " restart_queue
            if [[ $restart_queue =~ ^[Yy]$ ]]; then
                if [ -f "./start_queue_as_www.sh" ]; then
                    chmod +x ./start_queue_as_www.sh
                    ./start_queue_as_www.sh
                else
                    echo "未找到start_queue_as_www.sh脚本"
                fi
            fi
        fi
    else
        echo "✅ 所有队列进程都以非root用户运行"
    fi
else
    echo "未发现运行中的队列进程"
fi

# 3. 设置预防措施
echo ""
echo "3. 设置预防措施..."
echo "================================"

# 创建一个别名文件，提醒不要使用root运行队列
ALIAS_FILE="$HOME/.queue_aliases"
cat > "$ALIAS_FILE" << 'EOF'
# 队列管理别名 - 防止使用root用户
alias start-queue='echo "⚠️  请使用 ./start_queue_as_www.sh 而不是root用户启动队列"; false'
alias php-queue='echo "⚠️  请使用 sudo -u www php think queue:listen 而不是直接运行"; false'

# 正确的队列管理命令
alias queue-start='./start_queue_as_www.sh'
alias queue-stop='./stop_queue_listener.sh'
alias queue-status='ps aux | grep -E "(php.*think.*queue|queue:listen)" | grep -v grep'
alias queue-logs='tail -f runtime/logs/queue/*.log'
alias fix-permissions='./fix_permissions.sh'
EOF

echo "✅ 已创建队列管理别名文件: $ALIAS_FILE"
echo "要使用这些别名，请运行: source $ALIAS_FILE"

# 4. 创建监控脚本
MONITOR_SCRIPT="monitor_queue_permissions.sh"
cat > "$MONITOR_SCRIPT" << EOF
#!/bin/bash
# 队列权限监控脚本
# 建议添加到crontab中每小时运行一次

PROJECT_DIR="$PROJECT_DIR"
WEB_USER="$WEB_USER"

# 检查是否有新的root文件
ROOT_FILES=\$(find "\$PROJECT_DIR/runtime" -user root 2>/dev/null | wc -l)
if [ "\$ROOT_FILES" -gt 0 ]; then
    echo "\$(date): 发现 \$ROOT_FILES 个root用户文件，正在修复..." >> "\$PROJECT_DIR/runtime/logs/permission_monitor.log"
    sudo chown -R \$WEB_USER:\$WEB_USER "\$PROJECT_DIR/runtime"
    sudo chmod -R 777 "\$PROJECT_DIR/runtime"
fi

# 检查队列进程用户
ROOT_QUEUES=\$(ps aux | grep -E "(php.*think.*queue|queue:listen)" | grep -v grep | grep "^root" | wc -l)
if [ "\$ROOT_QUEUES" -gt 0 ]; then
    echo "\$(date): 发现 \$ROOT_QUEUES 个root队列进程" >> "\$PROJECT_DIR/runtime/logs/permission_monitor.log"
fi
EOF

chmod +x "$MONITOR_SCRIPT"
echo "✅ 已创建权限监控脚本: $MONITOR_SCRIPT"

# 5. 总结和建议
echo ""
echo "4. 总结和建议..."
echo "================================"
echo "✅ 权限检查完成"
echo ""
echo "预防root权限问题的最佳实践:"
echo "1. 始终使用 ./start_queue_as_www.sh 启动队列"
echo "2. 避免使用 sudo php think queue:listen"
echo "3. 定期运行 ./fix_permissions.sh 检查权限"
echo "4. 使用 ./check_and_prevent_root_issues.sh 进行全面检查"
echo "5. 考虑使用Supervisor管理队列进程"
echo ""
echo "常用命令:"
echo "- 启动队列: ./start_queue_as_www.sh"
echo "- 停止队列: ./stop_queue_listener.sh"
echo "- 修复权限: ./fix_permissions.sh"
echo "- 检查状态: ps aux | grep queue"
echo "- 查看日志: tail -f runtime/logs/queue/*.log"
echo ""
echo "如果要添加权限监控到crontab:"
echo "echo '0 * * * * $PROJECT_DIR/$MONITOR_SCRIPT' | crontab -"
