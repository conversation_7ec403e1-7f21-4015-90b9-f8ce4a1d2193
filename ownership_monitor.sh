#!/bin/bash

echo "启动文件所有权监听修复服务"
echo "================================"

# 确定web用户
if id "www" &>/dev/null; then
    WEB_USER="www"
elif id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    echo "未找到web用户，使用www"
    WEB_USER="www"
fi

echo "Web用户: $WEB_USER"
echo "监听间隔: 30秒"
echo "监听目录: runtime/"
echo "按 Ctrl+C 停止监听"
echo "================================"

# 创建日志目录
mkdir -p runtime/logs

# 监听循环
while true; do
    # 检查是否有root用户的文件
    ROOT_FILES=$(find runtime/ -user root 2>/dev/null)
    ROOT_COUNT=$(echo "$ROOT_FILES" | grep -v '^$' | wc -l)
    
    if [ "$ROOT_COUNT" -gt 0 ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S'): 发现 $ROOT_COUNT 个root文件，正在修复..."
        
        # 修复所有权
        chown -R $WEB_USER:$WEB_USER runtime/ 2>/dev/null
        
        # 确保关键目录权限
        chmod -R 777 runtime/cache/ 2>/dev/null || true
        chmod -R 777 runtime/log/ 2>/dev/null || true
        chmod -R 777 runtime/logs/ 2>/dev/null || true
        
        echo "$(date '+%Y-%m-%d %H:%M:%S'): 修复完成"
        
        # 记录到日志文件
        echo "$(date '+%Y-%m-%d %H:%M:%S'): 修复了 $ROOT_COUNT 个root文件" >> runtime/logs/ownership_monitor.log
    fi
    
    # 等待30秒
    sleep 30
done
