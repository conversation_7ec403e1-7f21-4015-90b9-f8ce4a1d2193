#!/bin/bash

echo "部署项目以www用户运行脚本"
echo "================================"

# 获取项目绝对路径
PROJECT_DIR=$(pwd)
echo "项目目录: $PROJECT_DIR"

# 检查www用户是否存在
if ! id "www" &>/dev/null; then
    echo "创建www用户..."
    sudo useradd -r -s /bin/bash -d /var/www www
    echo "www用户已创建"
fi

# 停止现有的队列进程
echo "停止现有的队列进程..."
./stop_queue_listener.sh

# 设置目录权限
echo "设置目录权限..."
sudo chown -R www:www "$PROJECT_DIR"
sudo chmod -R 755 "$PROJECT_DIR"

# 确保关键目录可写
sudo chmod -R 777 "$PROJECT_DIR/runtime"
sudo chmod -R 777 "$PROJECT_DIR/public/uploads" 2>/dev/null || true

# 创建日志目录
sudo -u www mkdir -p "$PROJECT_DIR/runtime/logs/queue"

# 选择部署方式
echo ""
echo "请选择部署方式:"
echo "1) 使用Supervisor（推荐）"
echo "2) 使用后台脚本"
echo "3) 仅设置权限，不启动队列"
read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo "配置Supervisor..."
        # 更新supervisor配置文件中的路径
        sed -i "s|/path/to/your/esop/project|$PROJECT_DIR|g" supervisor_queue.conf
        
        # 复制配置文件到supervisor目录
        sudo cp supervisor_queue.conf /etc/supervisor/conf.d/esop_queue.conf
        
        # 重新加载supervisor配置
        sudo supervisorctl reread
        sudo supervisorctl update
        
        # 启动队列
        sudo supervisorctl start esop_queues:*
        
        echo "Supervisor配置完成！"
        echo "查看状态: sudo supervisorctl status"
        echo "重启队列: sudo supervisorctl restart esop_queues:*"
        echo "停止队列: sudo supervisorctl stop esop_queues:*"
        ;;
    2)
        echo "使用后台脚本启动队列..."
        chmod +x start_queue_as_www.sh
        ./start_queue_as_www.sh
        ;;
    3)
        echo "仅设置权限完成，队列未启动"
        ;;
    *)
        echo "无效选择，仅设置权限"
        ;;
esac

echo ""
echo "部署完成！"
echo "项目现在以www用户运行"
echo ""
echo "常用命令:"
echo "- 查看队列进程: ps aux | grep 'php.*queue'"
echo "- 查看队列日志: tail -f runtime/logs/queue/*.log"
echo "- 手动启动队列: ./start_queue_as_www.sh"
echo "- 停止队列: ./stop_queue_listener.sh"
