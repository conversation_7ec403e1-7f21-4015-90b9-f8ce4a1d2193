#!/bin/bash

# 自动修复runtime目录文件所有权的定时任务脚本
# 建议添加到crontab: */10 * * * * /path/to/your/project/auto_fix_ownership.sh

# 切换到项目目录
cd "$(dirname "$0")"

# 确定web用户
if id "www" &>/dev/null; then
    WEB_USER="www"
elif id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    WEB_USER="www"
fi

# 检查是否有root用户的文件
ROOT_FILES=$(find runtime/ -user root 2>/dev/null | wc -l)

if [ "$ROOT_FILES" -gt 0 ]; then
    # 记录日志
    echo "$(date): 发现 $ROOT_FILES 个root文件，正在修复..." >> runtime/logs/ownership_fix.log
    
    # 修复所有权
    chown -R $WEB_USER:$WEB_USER runtime/
    chmod -R 777 runtime/cache/ 2>/dev/null || true
    chmod -R 777 runtime/log/ 2>/dev/null || true
    chmod -R 777 runtime/logs/ 2>/dev/null || true
    
    echo "$(date): 文件所有权修复完成" >> runtime/logs/ownership_fix.log
fi
