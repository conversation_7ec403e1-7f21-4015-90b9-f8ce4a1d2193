#!/bin/bash

echo "启动文件所有权监听服务"
echo "================================"

# 检查是否已经在运行
RUNNING_PIDS=$(pgrep -f "ownership_monitor.sh")
if [ -n "$RUNNING_PIDS" ]; then
    echo "监听服务已经在运行中"
    echo "PID: $RUNNING_PIDS"

    # 验证进程是否真的存在
    VALID_PIDS=""
    for PID in $RUNNING_PIDS; do
        if kill -0 $PID 2>/dev/null; then
            VALID_PIDS="$VALID_PIDS $PID"
        fi
    done

    if [ -n "$VALID_PIDS" ]; then
        echo "确认有效的运行进程:$VALID_PIDS"
        echo "如果要重启服务，请先运行: ./stop_ownership_monitor.sh"
        exit 1
    else
        echo "发现的PID已失效，清理残留信息..."
        # 清理可能的残留PID文件
        rm -f runtime/ownership_monitor.pid
    fi
fi

# 设置执行权限
chmod +x ownership_monitor.sh

# 检查启动方式
if [ "$1" == "--foreground" ] || [ "$1" == "-f" ]; then
    # 前台运行
    echo "在前台启动监听服务..."
    ./ownership_monitor.sh
else
    # 后台运行
    echo "在后台启动监听服务..."
    nohup ./ownership_monitor.sh > runtime/logs/ownership_monitor_output.log 2>&1 &
    MONITOR_PID=$!
    
    echo "监听服务已启动，PID: $MONITOR_PID"
    echo "日志文件: runtime/logs/ownership_monitor.log"
    echo "输出日志: runtime/logs/ownership_monitor_output.log"
    echo ""
    echo "停止服务: kill $MONITOR_PID"
    echo "或者运行: ./stop_ownership_monitor.sh"
    
    # 保存PID
    echo $MONITOR_PID > runtime/ownership_monitor.pid
fi
