#!/bin/bash

echo "启动文件所有权监听服务"
echo "================================"

# 检查是否已经在运行
if pgrep -f "ownership_monitor.sh" > /dev/null; then
    echo "监听服务已经在运行中"
    echo "PID: $(pgrep -f 'ownership_monitor.sh')"
    exit 1
fi

# 设置执行权限
chmod +x ownership_monitor.sh

# 检查启动方式
if [ "$1" == "--foreground" ] || [ "$1" == "-f" ]; then
    # 前台运行
    echo "在前台启动监听服务..."
    ./ownership_monitor.sh
else
    # 后台运行
    echo "在后台启动监听服务..."
    nohup ./ownership_monitor.sh > runtime/logs/ownership_monitor_output.log 2>&1 &
    MONITOR_PID=$!
    
    echo "监听服务已启动，PID: $MONITOR_PID"
    echo "日志文件: runtime/logs/ownership_monitor.log"
    echo "输出日志: runtime/logs/ownership_monitor_output.log"
    echo ""
    echo "停止服务: kill $MONITOR_PID"
    echo "或者运行: ./stop_ownership_monitor.sh"
    
    # 保存PID
    echo $MONITOR_PID > runtime/ownership_monitor.pid
fi
