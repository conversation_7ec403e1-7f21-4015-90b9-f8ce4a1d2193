#!/bin/bash

echo "修复项目权限脚本"
echo "================================"

# 获取项目目录
PROJECT_DIR=$(pwd)
echo "项目目录: $PROJECT_DIR"

# 检查www用户是否存在
if ! id "www" &>/dev/null; then
    echo "www用户不存在，尝试检查其他常用的web用户..."
    if id "www-data" &>/dev/null; then
        WEB_USER="www-data"
        echo "使用www-data用户"
    elif id "nginx" &>/dev/null; then
        WEB_USER="nginx"
        echo "使用nginx用户"
    elif id "apache" &>/dev/null; then
        WEB_USER="apache"
        echo "使用apache用户"
    else
        echo "警告: 未找到常用的web用户，将创建www用户"
        sudo useradd -r -s /bin/bash -d /var/www www
        WEB_USER="www"
    fi
else
    WEB_USER="www"
    echo "使用www用户"
fi

echo "Web用户: $WEB_USER"

# 修复runtime目录权限问题
echo "修复runtime目录权限..."

# 1. 递归修改runtime目录的所有者为web用户
sudo chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR/runtime"

# 2. 设置runtime目录权限为775（用户和组可读写执行，其他用户可读执行）
sudo chmod -R 775 "$PROJECT_DIR/runtime"

# 3. 特别处理cache和log目录，确保完全可写
sudo chmod -R 777 "$PROJECT_DIR/runtime/cache" 2>/dev/null || true
sudo chmod -R 777 "$PROJECT_DIR/runtime/log" 2>/dev/null || true
sudo chmod -R 777 "$PROJECT_DIR/runtime/logs" 2>/dev/null || true

# 4. 修复uploads目录权限（如果存在）
if [ -d "$PROJECT_DIR/public/uploads" ]; then
    echo "修复uploads目录权限..."
    sudo chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR/public/uploads"
    sudo chmod -R 775 "$PROJECT_DIR/public/uploads"
fi

# 5. 确保项目主目录的所有者正确
echo "设置项目主目录权限..."
sudo chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR"
sudo chmod -R 755 "$PROJECT_DIR"

# 6. 重新设置runtime相关目录为完全可写（这很重要）
sudo chmod -R 777 "$PROJECT_DIR/runtime"

# 7. 检查并显示当前权限状态
echo ""
echo "权限修复完成！当前状态："
echo "================================"
echo "runtime目录权限:"
ls -la "$PROJECT_DIR/runtime/" | head -10

if [ -d "$PROJECT_DIR/runtime/cache" ]; then
    echo ""
    echo "cache目录权限:"
    ls -la "$PROJECT_DIR/runtime/cache/" | head -5
fi

if [ -d "$PROJECT_DIR/runtime/log" ]; then
    echo ""
    echo "log目录权限:"
    ls -la "$PROJECT_DIR/runtime/log/" | head -5
fi

if [ -d "$PROJECT_DIR/runtime/logs" ]; then
    echo ""
    echo "logs目录权限:"
    ls -la "$PROJECT_DIR/runtime/logs/" | head -5
fi

echo ""
echo "权限修复完成！"
echo "建议："
echo "1. 始终使用 $WEB_USER 用户运行队列和其他后台任务"
echo "2. 避免使用root用户运行项目相关的进程"
echo "3. 如果必须使用root，请在运行后立即执行此脚本修复权限"
