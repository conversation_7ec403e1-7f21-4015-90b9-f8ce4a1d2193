#!/bin/bash

echo "以www用户启动队列监听服务脚本"
echo "================================"

# 检查当前用户
CURRENT_USER=$(whoami)
echo "当前用户: $CURRENT_USER"

# 检查www用户是否存在
if ! id "www" &>/dev/null; then
    echo "错误: www用户不存在，请先创建www用户"
    echo "可以使用以下命令创建:"
    echo "sudo useradd -r -s /bin/bash www"
    exit 1
fi

# 项目目录
PROJECT_DIR=$(pwd)
echo "项目目录: $PROJECT_DIR"

# 确保www用户对项目目录有适当的权限
echo "设置目录权限..."
sudo chown -R www:www "$PROJECT_DIR"
sudo chmod -R 755 "$PROJECT_DIR"

# 确保runtime目录可写
sudo chmod -R 777 "$PROJECT_DIR/runtime"

# 检查是否有参数
if [ "$1" == "--foreground" ] || [ "$1" == "-f" ]; then
    # 前台运行模式
    echo "在前台模式以www用户启动队列监听（按Ctrl+C可停止）..."
    echo "正在启动默认队列..."
    
    # 使用sudo -u www切换到www用户运行
    if command -v tmux &> /dev/null; then
        echo "使用tmux创建分屏会话..."
        # 创建新会话并启动第一个队列
        sudo -u www tmux new-session -d -s queue_listeners -c "$PROJECT_DIR" "php think queue:listen --queue default --verbose"
        # 在同一个窗口分屏并启动第二个队列
        sudo -u www tmux split-window -v -c "$PROJECT_DIR" "php think queue:listen --queue vesting --verbose"
        # 连接到会话
        sudo -u www tmux -2 attach-session -t queue_listeners
    else
        echo "tmux未安装，在单一控制台运行vesting队列（无法同时查看两个队列）"
        # 如果没有tmux，直接在当前控制台运行解禁队列
        sudo -u www php think queue:listen --queue vesting --verbose
    fi
else
    # 后台运行模式
    echo "在后台模式以www用户启动队列监听服务..."
    
    # 创建日志目录（如果不存在）
    sudo -u www mkdir -p runtime/logs/queue
    
    # 启动默认队列监听
    sudo -u www nohup php think queue:listen --queue default > runtime/logs/queue/default_$(date +%Y%m%d).log 2>&1 &
    DEFAULT_PID=$!
    echo "默认队列已启动，PID: $DEFAULT_PID"
    
    # 启动解禁任务队列监听
    sudo -u www nohup php think queue:listen --queue vesting > runtime/logs/queue/vesting_$(date +%Y%m%d).log 2>&1 &
    VESTING_PID=$!
    echo "解禁任务队列已启动，PID: $VESTING_PID"
    
    echo "队列监听服务已在后台以www用户启动!"
    echo "查看日志: runtime/logs/queue/default_$(date +%Y%m%d).log 和 runtime/logs/queue/vesting_$(date +%Y%m%d).log" 
    echo "要停止服务，请运行: sudo kill $DEFAULT_PID $VESTING_PID"
    
    # 保存PID到文件以便后续停止
    echo "$DEFAULT_PID $VESTING_PID" > runtime/queue_pids.txt
    sudo chown www:www runtime/queue_pids.txt
fi
