<?php

namespace app\common\job;

use think\queue\Job;
use think\Db;
use think\Exception;
use think\Log;
use app\common\model\EsopUserFundChangeRecords;
use app\common\model\EsopUserAsset;

/**
 * 解禁执行队列任务
 * 该任务用于按照规则中指定的execution_time执行解禁操作
 */
class VestingExecutionJob
{
    /**
     * 任务执行方法
     * @param Job $job 队列任务实例
     * @param array $data 任务数据
     */
    public function fire(Job $job, $data)
    {
        try {
            Log::record("开始执行解禁任务: " . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

            // 检查任务数据完整性
            if (!isset($data['vesting_record_id'])) {
                Log::record('解禁任务数据不完整', 'error');
                $job->delete();
                return;
            }

            // 获取任务参数
            $vestingRecordId = $data['vesting_record_id'];

            Log::record("任务参数: 解禁记录ID:{$vestingRecordId}", 'info');

            // 处理当天的解禁操作
            Log::record("开始处理解禁操作...", 'info');

            // 获取解禁记录
            $record = Db::name('esop_vesting_records')
                ->where('id', $vestingRecordId)
                ->where('vesting_status', 0) // 待解禁
                ->find();

            $this->processVesting($record);

            // 任务完成，从队列中删除
            $job->delete();
            Log::record("任务完成，已从队列中删除", 'info');
        } catch (Exception $e) {
            Log::record("解禁任务执行异常: " . $e->getMessage(), 'error');
            Log::record("异常文件: " . $e->getFile() . " 第" . $e->getLine() . "行", 'error');
            Log::record("堆栈跟踪: \n" . $e->getTraceAsString(), 'error');

            // 任务失败次数+1
            $attempts = $job->attempts();

            // 重试3次
            if ($attempts < 3) {
                // 延迟10分钟重试
                Log::record("这是第{$attempts}次尝试，10分钟后将重试", 'info');
                $job->release(600);
            } else {
                // 超过重试次数，记录失败日志并删除任务
                Log::record("已超过最大重试次数(3)，任务将被删除", 'error');

                Db::name('esop_system_logs')->insert([
                    'operation' => '解禁任务执行失败',
                    'ip_address' => '127.0.0.1',
                    'status' => 0,
                    'detail' => "解禁记录ID:{$data['vesting_record_id']} 错误: " . $e->getMessage(),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                $job->delete();
            }
        } finally {
            $this->createNextDayTask($record);
        }
    }


    /**
     * 处理解禁操作
     * @param int $record 解禁记录
     * @return void
     */
    private function processVesting($record)
    {

        // 开始事务
        Db::startTrans();
        Log::record("开始解禁事务...", 'info');
        try {
            if (!$record) {
                Log::record("未找到待解禁记录ID: {$record['id']}", 'error');
                Db::rollback();
                throw new \Exception("未找到待解禁记录ID: {$record['id']}");
            }

            Log::record("开始处理解禁记录ID: {$record['id']}", 'info');

            // 更新解禁状态
            $updatedAt = $this->getUpdatedAt($record);
            $updated = Db::name('esop_vesting_records')
                ->where('id', $record['id'])
                ->update([
                    'vesting_status' => 1, // 已解禁
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            if ($updated) {
                // 更新用户资产
                $userAsset = Db::name('esop_user_assets')
                    ->where('user_id', $record['target_user_id'])
                    ->find();

                if ($userAsset) {
                    // 解禁金额从待解冻资产转入可用资产
                    $vestingAmount = $record['vesting_amount'];
                    Log::record("用户ID: {$record['target_user_id']} 解禁金额: {$vestingAmount}", 'info');


                    EsopUserAsset::changeUserAsset($record['target_user_id'], [
                        'available_assets' => $vestingAmount,
                        'pending_assets' => -$vestingAmount,
                    ]);

                    $afterUserAsset = Db::name('esop_user_assets')
                        ->where('user_id', $record['target_user_id'])
                        ->find();

                    // 记录资金变化
                    EsopUserFundChangeRecords::addRecord(
                        $record['target_user_id'],
                        $vestingAmount,
                        EsopUserFundChangeRecords::ACTION_TYPE_UNLOCK,
                        $userAsset,
                        $afterUserAsset,
                        '系统自动解禁'
                    );

                    $userProfile = Db::name('esop_user_profiles')->where('user_id', $record['target_user_id'])->find();

                    // 记录资产变化日志
                    \app\common\model\EsopAssetChangeLog::batchRecordAssetChange(
                        [
                            [
                                'user_id' => $record['target_user_id'],
                                'b_account_id' => 0,
                                'stock_id' => 0,
                                'amount' => $vestingAmount,
                                'action_name' => '解禁',
                                'extra_name' => $userProfile['real_name'] ?? '',
                                'sub_name' => '资产',
                                'is_audit' => 0,
                                'audit_status' => 0,
                                'extends' => ['grant_id' => $record['grant_id'],'pending_amount' => $record['pending_amount']],
                                'relation_id' => $record['id'],
                                'created_at' =>  $updatedAt
                            ],
                            [
                                'user_id' => $record['target_user_id'],
                                'b_account_id' => 0,
                                'stock_id' => 0,
                                'amount' => -$vestingAmount,
                                'action_name' => '解禁',
                                'extra_name' => $userProfile['real_name'] ?? '',
                                'sub_name' => '待解禁资产',
                                'is_audit' => 0,
                                'audit_status' => 0,
                                'extends' => ['pending_amount' => $record['pending_amount'],'grant_id' => $record['grant_id']],
                                'relation_id' => $record['id'],
                                'created_at' =>  $updatedAt
                            ]
                        ]
                    );

                    Log::record("解禁记录ID: {$record['id']} 处理成功", 'info');
                } else {
                    $errorMsg = "用户ID: {$record['target_user_id']} 的资产记录不存在";
                    Log::record($errorMsg, 'error');
                    throw new \Exception($errorMsg);
                }
            } else {
                $errorMsg = "解禁记录ID: {$record['id']} 更新失败";
                Log::record($errorMsg, 'error');
                throw new \Exception($errorMsg);
            }

            // 提交事务
            Db::commit();
            Log::record("解禁事务提交成功", 'info');
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::record("事务回滚: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 计算下一执行日期
     * @param array $rule 规则信息
     * @param string $currentDate 当前日期
     * @return string|false 下一执行日期，如果无法确定则返回false
     */
    private function calculateNextExecutionDate($rule, $currentDate)
    {
        $executionType = $rule['execution_type'];

        switch ($executionType) {
            case 1: // 每天
                return date('Y-m-d', strtotime($currentDate) + 86400);

            case 2: // 工作日
                $nextDate = date('Y-m-d', strtotime($currentDate) + 86400);
                $dayOfWeek = date('N', strtotime($nextDate));

                // 如果是周六(6)或周日(7)，跳到下一个工作日(周一)
                if ($dayOfWeek >= 6) {
                    $daysToAdd = 8 - $dayOfWeek;
                    $nextDate = date('Y-m-d', strtotime($nextDate) + $daysToAdd * 86400);
                }
                return $nextDate;

            case 3: // 交易日
                $nextDate = date('Y-m-d', strtotime($currentDate) + 86400);
                $dayOfWeek = date('N', strtotime($nextDate));

                // 如果是周六(6)或周日(7)，跳到下一个工作日(周一)
                if ($dayOfWeek >= 6) {
                    $daysToAdd = 8 - $dayOfWeek;
                    $nextDate = date('Y-m-d', strtotime($nextDate) + $daysToAdd * 86400);
                }
                return $nextDate;

            default:
                return false;
        }
    }

    /**
     * 创建下一天的任务
     * @param int $record 解禁记录
     */
    private function createNextDayTask($record)
    {

        //查询第二天的解禁记录是否存在
        $nextDayRecord = Db::name('esop_vesting_records')
            ->where('grant_id', $record['grant_id'])
            ->where('vesting_date', date('Y-m-d', strtotime($record['vesting_date']) + 86400))
            ->find();

        if ($nextDayRecord) {
            Log::record("下一天 {$nextDayRecord['vesting_date']} 的解禁记录已存在，无需重复创建", 'info');
            return;
        }


        // 获取解禁规则信息
        $rule = Db::name('esop_vesting_rules')->where('id', $record['rule_id'])->find();
        if (!$rule || $rule['status'] != 1) {
            Log::record("规则不存在或已禁用，不创建下一天任务", 'info');
            return; // 规则不存在或已禁用，不创建下一天任务
        }

        // 获取授权记录
        $grant = Db::name('esop_grant_operation_logs')->where('id', $record['grant_id'])->find();
        if (!$grant) {
            Log::record("未找到授权记录ID:{$record['grant_id']}，不创建下一天任务", 'error');
            return;
        }

        //查询出解禁了多少天(包含未解禁成功的记录)
        $createdDays = Db::name('esop_vesting_records')
            ->where('grant_id', $record['grant_id'])
            ->where('rule_id', $record['rule_id'])
            ->where('target_user_id', $record['target_user_id'])
            ->count();

        // 计算下一执行日期
        $nextDate = $this->calculateNextExecutionDate($rule, $record['vesting_date']);
        if (!$nextDate) {
            Log::record("无法确定下一执行日期", 'info');
            return; // 无法确定下一执行日期
        }

        Log::record("下一执行日期: {$nextDate}", 'info');

        // 创建下一天的解禁记录
        Log::record("开始创建下一天的解禁记录...", 'info');
        try {
            // 仅针对"按天数解禁"类型进行下一天记录创建
            if ($rule['rule_type'] == 1) {
                // ---------------- 按天数解禁 ----------------
                $periods = (int)$rule['vesting_days'];

                // 如果剩余待解禁金额大于0，则创建下一天的解禁记录
                if (bccomp($record['pending_amount'], 0) > 0) {
                    // 当前剩余待解禁金额
                    $pendingAmount = $record['pending_amount'];

                    // 计算下一天解禁金额
                    $nextAmount = bcdiv($grant['grant_amount'], $periods, 2);

                    if (bccomp($nextAmount, 0, 2) <= 0) {
                        $nextAmount = $pendingAmount;
                    } else {
                        // 计算剩余天数
                        $remainingDays = $periods - $createdDays;
                        if ($remainingDays <= 0 ||  bccomp($record['pending_amount'], 0, 2) <= 0) {
                            Log::record("已达到总天数限制或待解禁金额为0，无需创建下一天记录", 'info');
                            return;
                        }

                        // 如果是最后一天，直接全部释放，保证总数精确
                        if ($remainingDays <= 1 || bccomp($pendingAmount, $nextAmount, 2) <= 0) {
                            $nextAmount = $pendingAmount;
                        }
                    }

                    $nextPendingAmount = bcsub($pendingAmount, $nextAmount, 2);

                    Log::record("下一天解禁金额: {$nextAmount}, 待解禁金额: {$nextPendingAmount}", 'info');

                    // 创建下一天的解禁记录
                    Db::name('esop_vesting_records')->insert([
                        'grant_id'       => $record['grant_id'],
                        'rule_id'        => $record['rule_id'],
                        'vesting_date'   => $nextDate,
                        'vesting_time'   => $rule['execution_time'],
                        'vesting_amount' => $nextAmount,
                        'pending_amount' => $nextPendingAmount,
                        'vesting_status' => 0,
                        'created_at'     => date('Y-m-d H:i:s'),
                        'updated_at'     => date('Y-m-d H:i:s'),
                        'target_user_id' => $record['target_user_id']
                    ]);

                    $newRecordId = Db::name('esop_vesting_records')->getLastInsID();
                    $this->pushToQueue($rule, $nextDate, $newRecordId);

                    Log::record("成功创建下一天的解禁记录", 'info');
                } else {
                    Log::record("已达到总天数限制，无需创建下一天记录", 'info');
                }
            } elseif ($rule['rule_type'] == 2) {
                // ---------------- 按比例解禁 ----------------
                $percentage = $rule['vesting_percentage'];
                $ratio      = bcdiv($percentage, 100, 2);
                $pendingAmount = $record['pending_amount'];

                if ($rule['is_decrease']) {
                    // 计算下一天解禁金额
                    $nextAmount = bcmul($pendingAmount, $ratio, 2);
                    if (bccomp($nextAmount, 0, 2) <= 0) {
                        $nextAmount = $pendingAmount;
                    }
                } else {
                    $nextAmount = bcmul($grant['grant_amount'], $ratio, 2);
                    //需要多少天
                    $remainNum = bcdiv(1, $ratio, 2);
                    $remainingDays = bccomp($remainNum, 0, 2) > 0 ? $remainNum + 1 : $remainNum;
                    // 如果是最后一天，直接全部释放，保证总数精确
                    if ($remainingDays <= 1 || bccomp($pendingAmount, $nextAmount, 2) <= 0) {
                        $nextAmount = $pendingAmount;
                    }
                }

                $nextPendingAmount = bcsub($pendingAmount, $nextAmount, 2);

                Log::record("按比例解禁，下一天解禁金额: {$nextAmount}, 待解禁金额: {$nextPendingAmount}", 'info');

                Db::name('esop_vesting_records')->insert([
                    'grant_id'       => $record['grant_id'],
                    'rule_id'        => $record['rule_id'],
                    'vesting_date'   => $nextDate,
                    'vesting_time'   => $rule['execution_time'],
                    'vesting_amount' => $nextAmount,
                    'pending_amount' => $nextPendingAmount,
                    'vesting_status' => 0,
                    'created_at'     => date('Y-m-d H:i:s'),
                    'updated_at'     => date('Y-m-d H:i:s'),
                    'target_user_id' => $grant['target_user_id']
                ]);

                $newRecordId = Db::name('esop_vesting_records')->getLastInsID();
                $this->pushToQueue($rule, $nextDate, $newRecordId);

                Log::record("成功创建下一天的解禁记录", 'info');
            } else {
                Log::record("未知的规则类型({$rule['rule_type']}), 无法创建下一天记录", 'error');
            }
        } catch (Exception $e) {
            Log::record("创建下一天解禁记录异常: " . $e->getMessage(), 'error');
            Log::record("异常文件: " . $e->getFile() . " 第" . $e->getLine() . "行", 'error');
            Log::record("堆栈跟踪: \n" . $e->getTraceAsString(), 'error');

            // 记录系统日志
            Db::name('esop_system_logs')->insert([
                'operation' => '创建下一天解禁记录失败',
                'ip_address' => '127.0.0.1',
                'status' => 0,
                'detail' => "授权ID:{$record['grant_id']} 规则ID:{$record['rule_id']} 下一日期:{$nextDate} 错误: " . $e->getMessage(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    private function pushToQueue($rule, $nextDate, $vestingRecordId)
    {
         // 有需要解禁的记录，创建下一天的任务
         $delay = $this->parseExecutionTimeAndDelay($rule['execution_time'], $nextDate);

         $jobData = [
            'vesting_record_id' => $vestingRecordId,
         ];

         Log::record("创建下一天任务: 日期 {$nextDate}, 执行时间 {$rule['execution_time']}, 延迟秒数 {$delay['delay_seconds']}", 'info');

         // 推送到队列，设置延迟执行
         \think\Queue::later($delay['delay_seconds'], 'app\\common\\job\\VestingExecutionJob', $jobData, 'vesting');
    }

    /**
     * 解析执行时间字符串并计算延迟秒数
     * @param string $executionTimeStr 执行时间字符串（如：1:30）
     * @param string $date 日期字符串（如：2024-06-01）
     * @return array 包含格式化的执行时间和延迟秒数的数组
     */
    private function parseExecutionTimeAndDelay($executionTimeStr, $date)
    {
        // 默认时间设为0:00
        $hour = 0;
        $minute = 0;

        // 解析时间字符串，支持"1:30"、"01:30"等格式
        if (preg_match('/^(\d{1,2}):(\d{1,2})$/', trim($executionTimeStr), $matches)) {
            $hour = (int)$matches[1];
            $minute = (int)$matches[2];
        }

        // 使用DateTime对象进行时间拼接和格式化
        try {
            // 创建目标日期时间对象
            $targetDateTime = new \DateTime($date);
            $targetDateTime->setTime($hour, $minute, 0);
            
            // 获取当前时间对象
            $currentDateTime = new \DateTime();
            
            // 使用diff计算时间差，返回DateInterval对象
            $interval = $currentDateTime->diff($targetDateTime);
            
            // 将DateInterval转换为秒数
            $seconds = $interval->days * 86400 + $interval->h * 3600 + $interval->i * 60 + $interval->s;
            
            // 如果目标时间早于当前时间，则时间差为负数
            if ($interval->invert) {
                $seconds = -$seconds;
            }
            
            // 如果目标时间已过，设置为立即执行(延迟1秒)
            $delay = max(1, $seconds);
            
            // 返回结果
            return [
                'execution_time' => $targetDateTime->format('Y-m-d H:i:s'),
                'delay_seconds' => $delay
            ];
        } catch (\Exception $e) {
            // 发生异常时，返回默认时间和延迟
            // 记录异常日志
            Log::record("解析执行时间异常: " . $e->getMessage(), 'error');
            
            return [
                'execution_time' => $date . ' 00:00:00',
                'delay_seconds' => 1
            ];
        }
    }

    private function getUpdatedAt($record)
    {
        $vestingTime = $record['vesting_time'];
        // 检查是否有秒
        if (preg_match('/^\d{2}:\d{2}:\d{2}$/', $vestingTime)) {
            $time = $vestingTime;
        } else {
            // 随机一个秒数
            $sec = str_pad(mt_rand(0, 59), 2, '0', STR_PAD_LEFT);
            $time = $vestingTime . ':' . $sec;
        }
        return $record['vesting_date'] . ' ' . $time;
    }
}
