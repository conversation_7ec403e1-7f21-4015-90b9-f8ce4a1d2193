### 注册
POST http://htgj.l43.cn/index.php/api/esop/user/register
Content-Type: application/json

{
    "phone": "13800138001", // 手机号
    "password": "123456", // 密码
    "password_confirm": "123456", // 确认密码
    "code": "123456", // 验证码
    "invitation_code": "ECRQVQ4F" // 邀请码
}

### 登录 密码登录
### 响应：{"code":1,"msg":"登录成功","data":{"id":1,"phone":"13800138001","token":"**********","expires_in":86400,"is_b_user":0}}
### is_b_user 是否为B端用户,1-是,0-否
POST http://htgj.l43.cn/index.php/api/esop/user/login
Content-Type: application/json

{
    "phone": "13555550000", // 手机号
    "password": "123456" // 密码
}

### 登录 验证码登录
POST http://htgj.l43.cn/index.php/api/esop/user/mobilelogin
Content-Type: application/json

{
    "phone": "13800138001", // 手机号
    "code": "123456" // 验证码
}


### 检查手机号是否存在
GET http://htgj.l43.cn/index.php/api/esop/user/check
Content-Type: application/json

{
    "phone": "13800138001" // 手机号
}

### 获取用户个人资料
GET http://htgj.l43.cn/index.php/api/esop/user/getUserProfile?phone=***********
Content-Type: application/json



### 更新用户个人资料
### 参数都是可选的
POST http://htgj.l43.cn/index.php/api/esop/user/updateUserProfile
Content-Type: application/json
token: **********

{
    "nickname": "史金芳", // 昵称
    "avatar": "http://htgj.l43.cn/uploads/********/**********12345678.jpg", // 头像
    "gender": "1", // 性别 0-保密 1-男 2-女
    "birth_date": "1960-02-25", // 出生日期
}

### 初始化用户个人资料
POST http://htgj.l43.cn/index.php/api/esop/user/initUserProfile
Content-Type: application/json

{
    "phone": "***********", // 手机号
    "real_name": "史金芳", // 真实姓名
    "gender": "1", // 性别 0-保密 1-男 2-女
    "birth_date": "1960-02-25", // 出生日期
    "id_card_no": "42062219600225612X", // 身份证号
    "marital_status": "1", // 婚姻状况 1-已婚 2-未婚 3-离异 4-丧偶
    "securities_account": "**********", // 证券账户
    "front_image": "http://htgj.l43.cn/uploads/********/**********12345678.jpg", // 身份证正面
    "back_image": "http://htgj.l43.cn/uploads/********/**********12345678.jpg", // 身份证背面
    "bank_name": "中国银行", // 开户行 （选填）
    "bank_branch": "中国银行北京分行", // 所属支行 （选填）
    "bank_account": "**********", // 银行卡号 （选填）
    "bank_image": "http://htgj.l43.cn/uploads/********/**********12345678.jpg" // 银行卡图片 （选填）
}


### 初始化银行卡信息
POST http://htgj.l43.cn/index.php/api/esop/user/initBankInfo
Content-Type: application/json

{
    "phone": "***********", // 手机号
    "bank_name": "中国银行", // 开户行 （选填）
    "bank_branch": "中国银行北京分行", // 所属支行 （选填）
    "bank_account": "**********", // 银行卡号 （选填）
    "bank_image": "http://htgj.l43.cn/uploads/********/**********12345678.jpg" // 银行卡图片 （选填）
}

    

### 图片文件上传
POST http://htgj.l43.cn/index.php/api/esop/upload/image
Content-Type: multipart/form-data
token: **********

file: **********12345678.jpg // 图片文件


### 获取系统配置
GET http://htgj.l43.cn/index.php/api/esop/config/info


### 获取APP下载地址
GET http://htgj.l43.cn/api/esop/config/appDownload


### 获取帮助中心分类文章
GET http://htgj.l43.cn/index.php/api/esop/help/index
Content-Type: application/json
token: **********


### 获取帮助中心文章
GET http://htgj.l43.cn/index.php/api/esop/help/detail?id=1
Content-Type: application/json
token: **********

### 搜索帮助中心文章
POST http://htgj.l43.cn/index.php/api/esop/help/search
Content-Type: application/json
token: **********
{
    "keyword": "1" // 搜索关键词 包含标题,内容,摘要
}


### 获取平台公告列表
### 参数：page 页码, limit 每页条数
### reponse： is_read 是否已读 0-未读 1-已读， is_b_user 是否为B端用户 0-否(平台公告) 1-是(B端公告)
GET http://htgj.l43.cn/index.php/api/esop/notice/index?page=1&limit=10&keyword=1
Content-Type: application/json
token: **********

### 获取平台公告详情
GET http://htgj.l43.cn/index.php/api/esop/notice/detail?id=2
Content-Type: application/json
token: **********

### 获取反馈列表, page 页码, limit 每页条数, status 状态(0-待处理, 1-已处理)
GET http://htgj.l43.cn/index.php/api/esop/feedback/list?page=1&limit=10&status=0
Content-Type: application/json
token: **********

### 提交反馈
POST http://htgj.l43.cn/index.php/api/esop/feedback/submit
Content-Type: application/json
token: **********

{
    "title": "测试反馈", // 反馈标题
    "content": "测试内容", // 反馈内容
    "images": ["http://htgj.l43.cn/uploads/********/**********12345678.jpg", "http://htgj.l43.cn/uploads/********/**********12345678.jpg"] // 反馈图片  
}


### 获取反馈详情
GET http://htgj.l43.cn/index.php/api/esop/feedback/detail?id=1
Content-Type: application/json
token: **********

### 获取用户实名认证状态
### response: {"code":1,"msg":"获取成功","data":{"has_profile":true,"audit_status":0,"audit_status_text":"待审核"}}
### audit_status 审核状态 0-待审核 1-审核通过 2-审核不通过
GET http://htgj.l43.cn/index.php/api/esop/user/realNameStatus
Content-Type: application/json
token: **********



### 记录用户已读公告
POST http://htgj.l43.cn/index.php/api/esop/notice/read
Content-Type: application/json
token: **********

{
    "notice_id": 1 // 公告ID
}

### 获取用户未读公告列表
GET http://htgj.l43.cn/index.php/api/esop/notice/unread
Content-Type: application/json
token: **********


### 修改手机号
POST http://htgj.l43.cn/index.php/api/esop/user/changePhone
Content-Type: application/json
token: **********

{
    "phone": "***********", // 新手机号
    "code": "123456" // 验证码
}

### 退出登录
GET  http://htgj.l43.cn/index.php/api/esop/user/logout
Content-Type: application/json
token: **********


### 发布平台公告 （B端用户）
POST http://htgj.l43.cn/index.php/api/esop/notice/publish
Content-Type: application/json
token: **********

{
    "title": "测试公告", // 公告标题
    "content": "测试内容", // 公告内容
    "image": "http://htgj.l43.cn/uploads/********/**********12345678.jpg" // 公告图片
}


### 获取B端用户信息(B端平台信息)
#   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'B端账户ID',
#   `account_name` varchar(100) NOT NULL COMMENT 'B端名称',
#   `ui_style_id` int(11) NOT NULL COMMENT 'UI风格ID',
#   `available_assets` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '可授权资产',
#   `invested_assets` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '已投权资产',
#   `exercise_fee_rate` decimal(5,2) NOT NULL DEFAULT '5.00' COMMENT '行权手续费率（%）',
#   `is_audit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否审核：0-否，1-是',
GET http://htgj.l43.cn/index.php/api/esop/user/bUserInfo
Content-Type: application/json
token: **********

### 设置B端平台信息
POST http://htgj.l43.cn/index.php/api/esop/user/setBUserInfo
Content-Type: application/json
token: **********

{
    "account_name": "测试B端", // B端名称
    "ui_style_id": 1, // UI风格ID
    "exercise_fee_rate": 5.00 // 行权手续费率（%）
}

### 生成邀请二维码
POST http://htgj.l43.cn/index.php/api/esop/user/generateInvitationQrcode
Content-Type: application/json
token: **********

{
    "register_url": "http://htgj.l43.cn/index.php/register" // 注册页面URL
}


### 设置交易密码
POST http://htgj.l43.cn/index.php/api/esop/user/setTradePassword
Content-Type: application/json
token: **********

{
    "phone": "***********", // 手机号
    "code": "123456", // 验证码
    "password": "123456", // 交易密码
    "password_confirm": "123456" // 确认密码
}

### 注销账号
POST http://htgj.l43.cn/index.php/api/esop/user/deleteAccount
Content-Type: application/json
token: **********

{
    "phone": "***********", // 手机号
    "code": "123456", // 验证码
    "is_read_risk_warning": 1 // 已阅读风险提示
}


### 修改登录密码(忘记密码+修改密码)
POST http://htgj.l43.cn/index.php/api/esop/user/changepwd
Content-Type: application/json
token: **********

{
    "phone": "***********", // 手机号
    "code": "123456", // 验证码
    "new_password": "123456" // 确认密码
}

### 关于我们
GET http://htgj.l43.cn/index.php/api/esop/config/about
Content-Type: application/json
token: **********

### 发送短信
### event事件类型: register-注册, changepwd-修改密码, changephone-修改手机号, setTradePassword-设置交易密码
POST http://htgj.l43.cn/index.php/api/esop/user/sendsms
Content-Type: application/json

{
    "phone": "***********", // 手机号
    "event": "register" // 事件类型 register-注册, changepwd-修改密码, changephone-修改手机号, setTradePassword-设置交易密码
}

### 获取平台ui风格
# style_name        varchar(50)                        not null comment '风格名称',
# main_color        varchar(10)                        not null comment '主色调',
# home_banner_url   varchar(255)                       null comment '首页底图',
# inner_banner_url  varchar(255)                       null comment '内页头部底图',
# member_banner_url varchar(255)                       null comment '会员中心头部底图',
# is_default        tinyint  default 0                 not null comment '是否默认风格：0-否，1-是',
GET http://htgj.l43.cn/index.php/api/esop/config/defaultStyle
Content-Type: application/json
token: **********

### 获取ui风格列表
GET http://htgj.l43.cn/index.php/api/esop/config/uiStyleList?page=1&limit=10
Content-Type: application/json
token: **********


### 添加授权    
POST http://htgj.l43.cn/index.php/api/esop/buser/addAuth
Content-Type: application/json
token: **********

{
    "grant_amount": 10000, // 授权数量
    "vesting_rule_id": 1, // 解禁规则ID
    "exercise_fee": 10000, // 行权手续费
    "target_user_id": 1 // 目标用户ID
}

# ### 获取用户资产
# "available_assets": "6900.000", // 可用资产，可换股资产
# "pending_assets": "70000.000", // 待解冻资产
# "stock_assets": "2000.000", // 股票资产
# "exchange_assets": "1000.000", // 已换股资产
# "total_assets": "78900.000" // 总资产
GET http://htgj.l43.cn/index.php/api/esop/assets/userAssets
Content-Type: application/json
token: **********

### 获取用户授权记录
### 参数：page 页码, limit 每页条数
### 响应：  
# "grant_amount": "365.0000",  // 授权数量
# "batch_no": "RSU2025第1批次", // 批次号
# "created_at": "2025-07-18 11:11:39",  // 授权时间
# "phone": "***********",   // 手机号   
# "real_name": "史金芳",    // 真实姓名
# "available_assets": "33503.0000",  // 可用资产（已解冻，可换股资产）
# "pending_assets": "-3.0000"    // 待解冻资产
GET http://htgj.l43.cn/index.php/api/esop/assets/userGrantList?page=1&limit=10
Content-Type: application/json
token: **********


### 获取用户授权记录(B端用户)
### 参数：page 页码, limit 每页条数
### 响应：  
# "grant_amount": "365.0000",  // 授权数量
# "batch_no": "RSU2025第1批次", // 批次号
# "created_at": "2025-07-18 11:11:39",  // 授权时间
# "phone": "***********",   // 手机号   
# "real_name": "史金芳",    // 真实姓名
# "available_assets": "33503.0000",  // 可用资产（已解冻，可换股资产）
# "pending_assets": "-3.0000"    // 待解冻资产
GET http://htgj.l43.cn/index.php/api/esop/assets/userGrantForBAccountList?page=1&limit=10
Content-Type: application/json
token: **********

### 转出资金
POST http://htgj.l43.cn/index.php/api/esop/assets/transfer
Content-Type: application/json
token: **********

{
    "to_phone": "***********", // 转出手机号
    "confirm_phone": "***********", // 确认转出手机号
    "amount": 10000, // 数量或金额
    "trade_password": "123456" // 交易密码
}

### 绑定证券账户
POST http://htgj.l43.cn/index.php/api/esop/user/bindSecuritiesAccount
Content-Type: application/json
token: **********

{
    "securities_account": "**********" // 证券账户
}

### 股票列表
    # stock_code          varchar(10)                        not null comment '股票代码',
    # stock_name          varchar(50)                        not null comment '股票名称',
    # stock_price         decimal(10, 4)                     not null comment '股票价格',
    # price_type          tinyint                            not null comment '价格类型：1-均价，2-约定价',
    # average_time        int                                null comment '均价时间',
    # exchangeable_amount decimal(20, 4)                     not null comment '可兑换数量',
    # is_on_shelf         tinyint  default 1                 not null comment '是否上架：0-否，1-是',
    # created_at          datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    # updated_at          datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    # stock_unit          int                                null comment '每手多少股'
GET http://htgj.l43.cn/index.php/api/esop/stock/index?page=1&limit=10
Content-Type: application/json
token: **********

### 发起换股
POST http://htgj.l43.cn/index.php/api/esop/stock/exchange
Content-Type: application/json
token: **********

{
    "stock_id": 1, // 股票ID
    "amount": 10000, // 数量
}

### 获取换股记录
# "stock_code": "000001", // 股票代码
# "stock_name": "平安银行", // 股票名称
# "stock_price": 10.00, // 股票价格
# "exchange_amount": 10000, // 兑换数量
# "created_at": "2025-01-01 12:00:00" // 创建时间
GET http://htgj.l43.cn/index.php/api/esop/stock/exchangeList?page=1&limit=10
Content-Type: application/json
token: **********


### 查询可授权用户
GET http://htgj.l43.cn/index.php/api/esop/assets/authUserList?page=1&limit=10&keyword=1
Content-Type: application/json
token: **********

### 搜索可授权用户
GET http://htgj.l43.cn/index.php/api/esop/assets/searchAuthUserList?page=1&limit=10&keyword=1
Content-Type: application/json
token: **********

### 进行行权
POST http://htgj.l43.cn/index.php/api/esop/stock/exercise
Content-Type: application/json
token: **********

{
    "stock_id": 1, // 股票ID
    "amount": 10000, // 数量
}

### 获取行权记录
# "stock_code": "000001", // 股票代码
# "stock_name": "平安银行", // 股票名称
# "stock_price": 10.00, // 股票价格
# "exercise_amount": 10000, // 行权数量
# "created_at": "2025-01-01 12:00:00" // 创建时间
GET http://htgj.l43.cn/index.php/api/esop/stock/exerciseList?page=1&limit=10
Content-Type: application/json
token: **********


### 获取轮播图列表
### 响应：
# "id": 1, // 轮播图ID
# "image_url": "http://htgj.l43.cn/uploads/********/**********12345678.jpg", // 图片URL
# "link_url": "http://htgj.l43.cn", // 链接URL
# "sort_order": 100, // 排序
# "status": 1, // 状态 0-禁用 1-启用
# "created_at": "2025-01-01 12:00:00" // 创建时间
GET http://htgj.l43.cn/index.php/api/esop/carousel/index?page=1&limit=10
Content-Type: application/json

### 获取b端管理员用户团队成员列表
### 参数：page 页码, limit 每页条数, keyword 搜索关键词
### 响应：
# "user_id": 1, // 用户ID
# "real_name": "史金芳", // 真实姓名
# "phone": "***********", // 手机号
# "gender": "1", // 性别
# "register_time": "2025-01-01 12:00:00" // 注册时间
GET http://htgj.l43.cn/index.php/api/esop/buser/teamMemberList?page=1&limit=10&keyword=1
Content-Type: application/json
token: **********

### 获取客服列表
### 参数：page 页码, limit 每页条数
### 响应：
# "id": 1, // 客服ID
# "icon": "http://htgj.l43.cn/uploads/********/**********12345678.jpg", // 客服图标
# "title": "客服标题", // 客服标题
# "account": **********, // 账号
# "created_at": "2025-01-01 12:00:00" // 创建时间
GET http://htgj.l43.cn/index.php/api/esop/customer/service/index?page=1&limit=10
Content-Type: application/json
token: **********


### 获取用户股票持有列表
### 参数：page 页码, limit 每页条数
### 响应：
# "stock_code": "000001", // 股票代码
# "stock_name": "平安银行", // 股票名称
# "stock_price": 10.00, // 股票价格
# "stock_amount": 10000, // 股票数量
# "created_at": "2025-01-01 12:00:00" // 创建时间
GET http://htgj.l43.cn/index.php/api/esop/stock/userStockList?page=1&limit=10
Content-Type: application/json
token: **********

### 根据股票ID获取最新价格
# 根据股票的价格类型自动调用相应函数获取最新价格
# price_type=1时调用getAveragePrice获取均价
# price_type=2时调用getLatestStockPrice获取实时价格
GET http://htgj.l43.cn/index.php/api/esop/stock/getLatestPrice?stock_id=1
Content-Type: application/json
token: **********

### 批量获取股票最新价格
# 批量获取多只股票的最新价格，最多支持50只股票
POST http://htgj.l43.cn/index.php/api/esop/stock/getBatchLatestPrice
Content-Type: application/json
token: **********

{
    "stock_ids": [1, 2, 3] // 股票ID数组
}

### 获取用户资产变化列表
### 参数：page 页码, limit 每页条数
### 响应：
# "id": 15, 
# "amount": "-0.270",  // 数量或金额
# "action_name": "解禁", // 操作名称
# "extra_name": "哈哈哈", // 扩展名称
# "sub_name": "待解决资产", // 子名称
# "is_audit": 0, // 是否需要审批
# "audit_status": 0, // 审批状态
# "audit_status_text": "待审批",
# "is_audit_text": "无需审批",
# "real_name": "哈哈哈", // 真实姓名
# "nickname": "4444", // 昵称
# "created_at": "2025-07-26 12:01:13", // 创建时间
# "extends": [] // 扩展信息
GET http://htgj.l43.cn/index.php/api/esop/assets/userAssetsChangeList?page=1&limit=10
Content-Type: application/json
token: **********


### 获取注销用户风险提示
GET http://htgj.l43.cn/index.php/api/esop/config/deleteAccountRisk
Content-Type: application/json
token: **********

### 获取隐私政策
GET http://htgj.l43.cn/index.php/api/esop/config/privacyPolicy
Content-Type: application/json

### 获取用户协议
GET http://htgj.l43.cn/index.php/api/esop/config/userAgreement
Content-Type: application/json

### 获取最新在线股票价格
GET http://htgj.l43.cn/index.php/api/esop/stock/getLatestPrice?stock_id=1
Content-Type: application/json
token: **********
