<?php

namespace app\common\model;

use think\Model;
use think\Db;

/**
 * ESOP APP版本管理模型
 */
class EsopAppVersion extends Model
{
    // 表名
    protected $name = 'esop_app_versions';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 追加属性
    protected $append = [
        'app_type_text',
        'status_text',
        'file_size_text'
    ];
    
    // APP类型列表
    protected static $appTypeList = [
        'android' => '安卓',
        'ios' => '苹果'
    ];
    
    // 状态列表
    protected static $statusList = [
        'active' => '启用',
        'inactive' => '禁用'
    ];
    
    /**
     * 获取APP类型文本
     */
    public function getAppTypeTextAttr($value, $data)
    {
        return isset($data['app_type']) ? self::$appTypeList[$data['app_type']] : '';
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return isset($data['status']) ? self::$statusList[$data['status']] : '';
    }
    
    /**
     * 获取文件大小文本
     */
    public function getFileSizeTextAttr($value, $data)
    {
        if (!isset($data['file_size']) || !$data['file_size']) {
            return '';
        }
        
        $size = $data['file_size'];
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        
        while ($size >= 1024 && $i < count($units) - 1) {
            $size /= 1024;
            $i++;
        }
        
        return round($size, 2) . ' ' . $units[$i];
    }
    
    /**
     * 创建新版本
     * 
     * @param array $data 版本数据
     * @return bool|EsopAppVersion
     */
    public static function createVersion($data)
    {
        try {
            Db::startTrans();
            
            // 如果设置为当前版本，先将同类型的其他版本设为非当前版本
            if (isset($data['is_current']) && $data['is_current']) {
                self::where('app_type', $data['app_type'])
                    ->where('is_current', 1)
                    ->update(['is_current' => 0]);
            }
            
            // 创建新版本
            $version = self::create($data);
            
            Db::commit();
            return $version;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }
    
    /**
     * 设置当前版本
     * 
     * @param int $id 版本ID
     * @return bool
     */
    public static function setCurrent($id)
    {
        try {
            Db::startTrans();
            
            $version = self::find($id);
            if (!$version) {
                return false;
            }
            
            // 将同类型的其他版本设为非当前版本
            self::where('app_type', $version['app_type'])
                ->where('is_current', 1)
                ->update(['is_current' => 0]);
            
            // 设置当前版本
            $version->is_current = 1;
            $version->save();
            
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }
    
    /**
     * 获取当前版本
     * 
     * @param string $appType APP类型
     * @return EsopAppVersion|null
     */
    public static function getCurrentVersion($appType)
    {
        return self::where('app_type', $appType)
            ->where('is_current', 1)
            ->where('status', 'active')
            ->find();
    }
    
    /**
     * 获取版本列表
     * 
     * @param string $appType APP类型
     * @param int $limit 限制数量
     * @return array
     */
    public static function getVersionList($appType = '', $limit = 10)
    {
        $query = self::order('version_code', 'desc');
        
        if ($appType) {
            $query->where('app_type', $appType);
        }
        
        if ($limit > 0) {
            $query->limit($limit);
        }
        
        return $query->select();
    }
    
    /**
     * 检查版本更新
     * 
     * @param string $appType APP类型
     * @param int $currentVersionCode 当前版本号
     * @return array|null
     */
    public static function checkUpdate($appType, $currentVersionCode)
    {
        $latestVersion = self::where('app_type', $appType)
            ->where('status', 'active')
            ->where('version_code', '>', $currentVersionCode)
            ->order('version_code', 'desc')
            ->find();
        
        if ($latestVersion) {
            return [
                'has_update' => true,
                'version_name' => $latestVersion['version_name'],
                'version_code' => $latestVersion['version_code'],
                'download_url' => $latestVersion['file_url'],
                'file_size' => $latestVersion['file_size'],
                'is_force_update' => $latestVersion['is_force_update'],
                'update_content' => $latestVersion['update_content']
            ];
        }
        
        return ['has_update' => false];
    }
    
    /**
     * 删除版本（软删除，设为禁用状态）
     * 
     * @param int $id 版本ID
     * @return bool
     */
    public static function deleteVersion($id)
    {
        $version = self::find($id);
        if (!$version) {
            return false;
        }
        
        // 如果是当前版本，不允许删除
        if ($version['is_current']) {
            return false;
        }
        
        $version->status = 'inactive';
        return $version->save();
    }
}
