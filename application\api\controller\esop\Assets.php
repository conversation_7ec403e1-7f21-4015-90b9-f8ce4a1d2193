<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopUserAsset;
use app\common\model\EsopUserFundChangeRecords; // 引入资金变化记录模型
use app\common\model\EsopUserStockRecords; // 引入用户股票记录模型
use app\common\model\EsopStockManagement;  // 引入股票管理模型
use app\common\model\EsopAssetChangeLog;  // 引入资产变化日志模型
use think\Db;

/**
 * 用户资产接口
 */
class Assets extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 获取用户资产
     * 
     * @ApiTitle    (获取用户资产)
     * @ApiSummary  (获取当前登录用户的资产信息)
     * @ApiMethod   (GET)
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"**********","data":{"available_assets":"100.000","pending_assets":"50.000"}})
     */
    public function userAssets()
    {
        $userId = $this->auth->id;
        
        // 获取用户资产
        $userAsset = EsopUserAsset::where('user_id', $userId)->find();
        $availableAssets = (string)$userAsset->available_assets;
        $pendingAssets   = (string)$userAsset->pending_assets;
        $lockedAssets  = (string)$userAsset->locked_assets;

        // ===================== 新增逻辑：计算持有股票市值 =====================
        // 查询用户持有的股票记录
        $stockRecords = EsopUserStockRecords::where('user_id', $userId)->select();
        $stockAsset   = '0'; // 先用字符串计算，后续再格式化

        if (!empty($stockRecords)) {
            // 提取所有股票ID
            $stockIds = [];
            foreach ($stockRecords as $sr) {
                $stockIds[] = $sr['stock_id'];
            }

            // 查询对应股票价格，返回键值对  [id => price]
            $priceMap = EsopStockManagement::whereIn('id', $stockIds)->column('stock_price', 'id');

            // 计算股票资产：数量 * 当前价格 之和
            foreach ($stockRecords as $sr) {
                $price = isset($priceMap[$sr['stock_id']]) ? (string)$priceMap[$sr['stock_id']] : '0';
                $value = bcmul((string)$sr['amount'], $price, 8);
                $stockAsset = bcadd($stockAsset, $value, 8);
            }
        }
        // 格式化股票资产，保留3位小数
        $stockAssetFormatted = number_format($stockAsset, 3, '.', '');
        // ===================================================================

        // ===================== 新增逻辑：计算换股资产 =====================
        // 换股资产 = esop_exchange_records表中当前用户所有exchange_amount之和
        $exchangeAsset = \app\common\model\EsopExchangeRecords::where('user_id', $userId)->sum('exchange_amount');
        $exchangeAsset = $exchangeAsset ? (string)$exchangeAsset : '0';
        $exchangeAssetFormatted = number_format($exchangeAsset, 3, '.', '');
        // ===================================================================

        // 计算总资产 = 可用资产 + 待解冻资产 + 股票资产 + 锁定资产
        $totalAsset = bcadd(bcadd(bcadd($availableAssets, $pendingAssets, 8), $stockAsset, 8), $lockedAssets, 8);
        $totalAssetFormatted = number_format($totalAsset, 3, '.', '');

        // 返回数据
        $data = [
            'available_assets' => number_format($availableAssets, 3, '.', ''),
            'pending_assets'   => number_format($pendingAssets, 3, '.', ''),
            'stock_assets'     => $stockAssetFormatted,
            'exchange_assets'  => $exchangeAssetFormatted, // 新增字段
            'total_assets'     => $totalAssetFormatted,
        ];
        
        return $this->success('获取成功', $data);
    }
    
    /**
     * 获取用户授权记录
     * 
     * @ApiTitle    (获取用户授权记录)
     * @ApiSummary  (获取当前登录用户的授权记录列表)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"**********","data":{"total":1,"per_page":10,"current_page":1,"last_page":1,"data":[...]}})
     */
    public function userGrantList()
    {
        $userId = $this->auth->id;
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);
        $keyword = $this->request->get('keyword', '');
        
        $query = DB::name('esop_grant_operation_logs')
            ->alias('log')
            ->join('esop_users u', 'u.id = log.target_user_id')
            ->join('esop_user_profiles up', 'up.user_id = log.target_user_id')
            // 移除用户总资产关联，改为后续统计每笔授权的解禁情况
            // ->join('esop_user_assets ua', 'ua.user_id = log.target_user_id')
            ->where('log.target_user_id', $userId)
            ->field('log.id,log.grant_amount,log.batch_no,log.b_account_alias,log.created_at,u.phone,up.gender,up.real_name');

        if($keyword != ''){
            $query->where('u.phone', 'like', "%{$keyword}%")
                ->whereOr('up.real_name', 'like', "%{$keyword}%");
        }
        
        $list = $query->order('log.id', 'desc')
            ->paginate($limit, false, ['page' => $page]);

        // 批量获取所有授权ID，避免N+1查询
        $rows = $list->items();
        $grantIds = array_column($rows, 'id');
        $vestingMap = [];
        if (!empty($grantIds)) {
            // 一次性聚合查询所有授权的解禁统计
            $vestingList = DB::name('esop_vesting_records')
                ->where('grant_id', 'in', $grantIds)
                ->field([
                    'grant_id',
                    'SUM(CASE WHEN vesting_status=1 THEN vesting_amount ELSE 0 END) as current_vested_amount',
                ])
                ->group('grant_id')
                ->select();
            // 构建grant_id到统计结果的映射
            foreach ($vestingList as $v) {
                $vestingMap[$v['grant_id']] = [
                    'current_vested_amount' => $v['current_vested_amount'] ?? 0,
                ];
            }
        }
        // 合并统计结果到每条授权记录
        foreach ($rows as &$row) {
            $available = $vestingMap[$row['id']]['current_vested_amount'] ?? 0;
            $pending = bcsub($row['grant_amount'], $available, 2);
            // 如果没有任何解禁记录，则待解禁=授权数量
            if ($available == 0 && $pending == 0) {
                $pending = $row['grant_amount'];
            }
            $row['available_assets'] = $available;
            $row['pending_assets'] = $pending;
        }

        $data = [
            'total' => $list->total(),
            'per_page' => $list->listRows(),
            'current_page' => $list->currentPage(),
            'last_page' => $list->lastPage(),
            'rows' => $rows
        ];
        
        return $this->success('获取成功', $data);
    }


    /**
     * 获取用户授权记录
     * 
     * @ApiTitle    (获取用户授权记录)
     * @ApiSummary  (获取当前登录用户的授权记录列表)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"**********","data":{"total":1,"per_page":10,"current_page":1,"last_page":1,"data":[...]}})
     */
    public function userGrantForBAccountList()
    {
        $userId = $this->auth->id;
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);
        $keyword = $this->request->get('keyword', '');
        
        $query = DB::name('esop_grant_operation_logs')
            ->alias('log')
            ->join('esop_users u', 'u.id = log.target_user_id')
            ->join('esop_user_profiles up', 'up.user_id = log.target_user_id')
            ->where('log.user_id', $userId)
            ->field('log.id,log.grant_amount,log.batch_no,log.b_account_alias,log.created_at,u.phone,up.gender,up.real_name,up.audit_status');

        if($keyword != ''){
            $query->where('u.phone', 'like', "%{$keyword}%")
                ->whereOr('up.real_name', 'like', "%{$keyword}%");
        }
        
        $list = $query->order('log.id', 'desc')
            ->paginate($limit, false, ['page' => $page]);

        // 批量获取所有授权ID，避免N+1查询
        $rows = $list->items();
        $grantIds = array_column($rows, 'id');
        $vestingMap = [];
        if (!empty($grantIds)) {
            // 一次性聚合查询所有授权的解禁统计
            $vestingList = DB::name('esop_vesting_records')
                ->where('grant_id', 'in', $grantIds)
                ->field([
                    'grant_id',
                    'SUM(CASE WHEN vesting_status=1 THEN vesting_amount ELSE 0 END) as current_vested_amount',
                ])
                ->group('grant_id')
                ->select();
            // 构建grant_id到统计结果的映射
            foreach ($vestingList as $v) {
                $vestingMap[$v['grant_id']] = [
                    'current_vested_amount' => $v['current_vested_amount'] ?? 0,
                ];
            }
        }
        // 合并统计结果到每条授权记录
        foreach ($rows as &$row) {
            $available = $vestingMap[$row['id']]['current_vested_amount'] ?? 0;
            $pending = bcsub($row['grant_amount'], $available, 2);
            // 如果没有任何解禁记录，则待解禁=授权数量
            if ($available == 0 && $pending == 0) {
                $pending = $row['grant_amount'];
            }
            $row['available_assets'] = $available;
            $row['pending_assets'] = $pending;
        }

        $data = [
            'total' => $list->total(),
            'per_page' => $list->listRows(),
            'current_page' => $list->currentPage(),
            'last_page' => $list->lastPage(),
            'rows' => $rows
        ];
        
        return $this->success('获取成功', $data);
    }

    /**
     * 查询可授权用户
     * 
     * @ApiTitle    (查询可授权用户)
     * @ApiSummary  (根据关键词查询可用进行transfer的用户列表)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiParams   (name="keyword", type="string", required=false, description="搜索关键词，可搜索手机号、真实姓名")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"**********","data":{"total":1,"per_page":10,"current_page":1,"last_page":1,"data":[...]}})
     */
    public function authUserList()
    {
        $userId = $this->auth->id;
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);
        $keyword = $this->request->get('keyword', '');

        // 获取当前用户的b_account_id
        $currentUserBAccountId = \app\common\model\EsopInvitationRelation::where('invitee_id', $userId)->value('b_account_id');
        
        if (!$currentUserBAccountId) {
            return $this->success('获取成功', [
                'total' => 0,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => 0,
                'data' => []
            ]);
        }

        // 递归获取所有被邀请用户ID（包括间接邀请）
        $allInvitedUserIds = $this->getAllInvitedUserIds($userId, $currentUserBAccountId);
        
        if (empty($allInvitedUserIds)) {
            return $this->success('获取成功', [
                'total' => 0,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => 0,
                'data' => []
            ]);
        }

        // 查询所有被邀请用户的信息
        $query = \app\common\model\EsopUser::alias('u')
            ->join('esop_user_profiles p', 'p.user_id = u.id AND p.audit_status = 1', 'INNER')
            ->where('u.status', 1)
            ->whereIn('u.id', $allInvitedUserIds);
            
        // 关键词搜索
        if ($keyword !== '') {
            $query->where(function ($sub) use ($keyword) {
                $sub->where('u.phone', 'like', "%{$keyword}%")
                    ->whereOr('p.real_name', 'like', "%{$keyword}%");
            });
        }

        // 字段选择
        $query->field('u.id,u.phone,u.created_at,p.real_name,p.avatar,p.gender');

        // 分页查询
        $list = $query->order('u.id', 'desc')
                     ->paginate($limit, false, ['page' => $page]);

        // 格式化结果
        $result = [];
        foreach ($list as $item) {
            $genderText = '';
            if (isset($item['gender'])) {
                $genderText = \app\common\model\EsopUserProfile::$genderList[$item['gender']] ?? '';
            }

            $result[] = [
                'id' => $item['id'],
                'phone' => $item['phone'],
                'real_name' => $item['real_name'] ?? '',
                'avatar' => $item['avatar'] ?? '',
                'gender' => $item['gender'] ?? '',
                'gender_text' => $genderText,
                'created_at' => $item['created_at'],
            ];
        }
        
        $data = [
            'total' => $list->total(),
            'per_page' => $list->listRows(),
            'current_page' => $list->currentPage(),
            'last_page' => $list->lastPage(),
            'data' => $result
        ];
        
        return $this->success('获取成功', $data);
    }


    /**
     * 查询可授权用户
     * 
     * @ApiTitle    (查询可授权用户)
     * @ApiSummary  (根据关键词查询可用进行transfer的用户列表)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiParams   (name="keyword", type="string", required=false, description="搜索关键词，可搜索手机号、真实姓名")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"**********","data":{"total":1,"per_page":10,"current_page":1,"last_page":1,"data":[...]}})
     */
    public function searchAuthUserList()
    {
        $userId = $this->auth->id;
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);
        $keyword = $this->request->get('keyword', '');

        // 获取当前用户的b_account_id
        $currentUserBAccountId = \app\common\model\EsopInvitationRelation::where('invitee_id', $userId)->value('b_account_id');
        
        if (!$currentUserBAccountId) {
            return $this->success('获取成功', [
                'total' => 0,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => 0,
                'data' => []
            ]);
        }

        // 递归获取所有被邀请用户ID（包括间接邀请）
        $allInvitedUserIds = $this->searchAllInvitedUserIds($userId, $currentUserBAccountId);
        
        if (empty($allInvitedUserIds)) {
            return $this->success('获取成功', [
                'total' => 0,
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => 0,
                'data' => []
            ]);
        }

        // 查询所有被邀请用户的信息
        $query = \app\common\model\EsopUser::alias('u')
            ->join('esop_user_profiles p', 'p.user_id = u.id', 'INNER')
            ->where('u.status', 1)
            ->whereIn('u.id', $allInvitedUserIds);
            
        // 关键词搜索
        if ($keyword !== '') {
            $query->where(function ($sub) use ($keyword) {
                $sub->where('u.phone', 'like', "%{$keyword}%")
                    ->whereOr('p.real_name', 'like', "%{$keyword}%");
            });
        }

        // 字段选择
        $query->field('u.id,u.phone,u.created_at,p.real_name,p.avatar,p.gender');

        // 分页查询
        $list = $query->order('u.id', 'desc')
                     ->paginate($limit, false, ['page' => $page]);

        // 格式化结果
        $result = [];
        foreach ($list as $item) {
            $genderText = '';
            if (isset($item['gender'])) {
                $genderText = \app\common\model\EsopUserProfile::$genderList[$item['gender']] ?? '';
            }

            $result[] = [
                'id' => $item['id'],
                'phone' => $item['phone'],
                'real_name' => $item['real_name'] ?? '',
                'avatar' => $item['avatar'] ?? '',
                'gender' => $item['gender'] ?? '',
                'gender_text' => $genderText,
                'created_at' => $item['created_at'],
            ];
        }
        
        $data = [
            'total' => $list->total(),
            'per_page' => $list->listRows(),
            'current_page' => $list->currentPage(),
            'last_page' => $list->lastPage(),
            'data' => $result
        ];
        
        return $this->success('获取成功', $data);
    }

    /**
     * 递归获取所有被邀请用户ID
     * 
     * @param int $userId 当前用户ID
     * @param int $bAccountId B端账户ID
     * @return array 所有被邀请用户ID数组
     */
    private function searchAllInvitedUserIds($userId, $bAccountId)
    {
        // 一次性查询所有相关的邀请关系数据
        $allRelations = \app\common\model\EsopInvitationRelation::alias('ir')
            ->join('esop_user_profiles p', 'p.user_id = ir.invitee_id', 'INNER')
            ->join('esop_users u', 'u.id = ir.invitee_id AND u.status = 1', 'INNER')
            ->where('ir.b_account_id', $bAccountId)
            ->field('ir.inviter_id, ir.invitee_id,ir.root_id')
            ->select();
        
        // 构建邀请关系映射表
        $invitationMap = [];
        foreach ($allRelations as $relation) {
            $inviterId = $relation['inviter_id'];
            $inviteeId = $relation['invitee_id'];
            
            if (!isset($invitationMap[$inviterId])) {
                $invitationMap[$inviterId] = [];
            }
            $invitationMap[$inviterId][] = $inviteeId;
        }
        
        // 在内存中递归查找所有被邀请用户
        $allUserIds = [];
        $processedIds = [];
        
        // 使用队列进行广度优先搜索
        $queue = [$userId];
        
        while (!empty($queue)) {
            $currentUserId = array_shift($queue);
            
            if (in_array($currentUserId, $processedIds)) {
                continue;
            }
            
            $processedIds[] = $currentUserId;
            
            // 从内存映射表中获取当前用户直接邀请的用户
            if (isset($invitationMap[$currentUserId])) {
                foreach ($invitationMap[$currentUserId] as $invitedUserId) {
                    // 避免重复处理
                    if (!in_array($invitedUserId, $processedIds)) {
                        $allUserIds[] = $invitedUserId;
                        $queue[] = $invitedUserId; // 将邀请的用户加入队列，继续递归查询
                    }
                }
            }
        }
        
        return array_unique($allUserIds);
    }

    /**
     * 递归获取所有被邀请用户ID
     * 
     * @param int $userId 当前用户ID
     * @param int $bAccountId B端账户ID
     * @return array 所有被邀请用户ID数组
     */
    private function getAllInvitedUserIds($userId, $bAccountId)
    {
        // 一次性查询所有相关的邀请关系数据
        $allRelations = \app\common\model\EsopInvitationRelation::alias('ir')
            ->join('esop_user_profiles p', 'p.user_id = ir.invitee_id AND p.audit_status = 1', 'INNER')
            ->join('esop_users u', 'u.id = ir.invitee_id AND u.status = 1', 'INNER')
            ->where('ir.b_account_id', $bAccountId)
            ->field('ir.inviter_id, ir.invitee_id,ir.root_id')
            ->select();
        
        // 构建邀请关系映射表
        $invitationMap = [];
        foreach ($allRelations as $relation) {
            $inviterId = $relation['inviter_id'];
            $inviteeId = $relation['invitee_id'];
            
            if (!isset($invitationMap[$inviterId])) {
                $invitationMap[$inviterId] = [];
            }
            $invitationMap[$inviterId][] = $inviteeId;
        }
        
        // 在内存中递归查找所有被邀请用户
        $allUserIds = [];
        $processedIds = [];
        
        // 使用队列进行广度优先搜索
        $queue = [$userId];
        
        while (!empty($queue)) {
            $currentUserId = array_shift($queue);
            
            if (in_array($currentUserId, $processedIds)) {
                continue;
            }
            
            $processedIds[] = $currentUserId;
            
            // 从内存映射表中获取当前用户直接邀请的用户
            if (isset($invitationMap[$currentUserId])) {
                foreach ($invitationMap[$currentUserId] as $invitedUserId) {
                    // 避免重复处理
                    if (!in_array($invitedUserId, $processedIds)) {
                        $allUserIds[] = $invitedUserId;
                        $queue[] = $invitedUserId; // 将邀请的用户加入队列，继续递归查询
                    }
                }
            }
        }
        
        return array_unique($allUserIds);
    }

    /**
     * 转出资金
     * 
     * @ApiTitle    (转出资金)
     * @ApiSummary  (用户资金转出到其他用户账户)
     * @ApiMethod   (POST)
     * @ApiParams   (name="amount", type="number", required=true, description="转出金额")
     * @ApiParams   (name="to_phone", type="string", required=true, description="转出手机号")
     * @ApiParams   (name="confirm_phone", type="string", required=true, description="确认转出手机号")
     * @ApiParams   (name="trade_password", type="string", required=true, description="交易密码")
     * @ApiParams   (name="remark", type="string", required=false, description="备注")
     * @ApiReturn   ({"code":1,"msg":"转出成功","time":"**********","data":{}})
     */
    public function transfer()
    {
        $userId = $this->auth->id;
        
        // 获取参数
        $amount = $this->request->post('amount/f');
        $toPhone = $this->request->post('to_phone');
        $confirmPhone = $this->request->post('confirm_phone');
        $tradePassword = $this->request->post('trade_password');
        $remark = $this->request->post('remark', '');
        
        // 参数验证
        if ($amount <= 0) {
            return $this->error('转出金额必须大于0');
        }
        
        if ($toPhone != $confirmPhone) {
            return $this->error('两次输入的手机号不一致');
        }
        
        // 查找目标用户
        $targetUser = \app\common\model\EsopUser::where('phone', $toPhone)
            ->where('status', 1)
            ->find();
            
        if (!$targetUser) {
            return $this->error('转出目标用户不存在');
        }
        
        // 不能转给自己
        if ($targetUser->id == $userId) {
            return $this->error('不能转给自己');
        }
        
        // 验证交易密码
        $userProfile = \app\common\model\EsopUserProfile::where('user_id', $userId)
            ->find();
            
        if (!$userProfile || !$userProfile->trade_password) {
            return $this->error('请先设置交易密码');
        }
        
        if (!\app\common\model\EsopUserProfile::verifyTradePassword($userId, $tradePassword)) {
            return $this->error('交易密码不正确');
        }
        
        
        
        // 获取用户资产记录
        $userAsset = EsopUserAsset::where('user_id', $userId)->find();
        if (!$userAsset) {
            return $this->error('您的资产记录不存在');
        }
        
        // 获取系统转账控制配置
        $systemConfig = \app\common\model\EsopSystemConfig::where('id', 1)->find();
        // 获取全局转账控制配置，默认为0（无控制）
        $globalTransferControl = $systemConfig ? $systemConfig->transfer_control : 0;
        // 获取转账审批开关，默认为0（关闭）
        $transferApprovalSwitch = $systemConfig ? $systemConfig->transfer_approval_switch : 0;

        // 判断用户资产表是否设置了转账控制（优先使用用户资产表的设置，否则使用全局设置）
        // transfer_control为null或未设置时，使用全局控制，否则使用用户资产的控制
        if (isset($userAsset->transfer_control) && $userAsset->transfer_control > 0) {
            $transferControl = $userAsset->transfer_control;
        } else {
            $transferControl = $globalTransferControl;
        }
                           
        if ($transferControl == 2) {
            return $this->error('系统已禁止转账');
        }
        
        // 同B端限制检查
        if ($transferControl == 1) {
            // 获取当前用户和目标用户的B端账户
            $currentUserBAccount = \app\common\model\EsopInvitationRelation::where('invitee_id', $userId)
                ->value('b_account_id');
                
            $targetUserBAccount = \app\common\model\EsopInvitationRelation::where('invitee_id', $targetUser->id)
                ->value('b_account_id');
                
            if ($currentUserBAccount !== $targetUserBAccount || !$currentUserBAccount) {
                return $this->error('只能转账给同一B端账户下的用户');
            }
        }
        
        // 检查可用余额
        if ($userAsset->available_assets < $amount) {
            return $this->error('可用资产不足');
        }
        
        // 开始转账流程
        \think\Db::startTrans();
        try {
            // 获取转出方转账前资产数据
            $senderBeforeAsset = $userAsset->toArray();
            
            // 获取接收方转账前资产数据
            $targetUserAsset = EsopUserAsset::where('user_id', $targetUser->id)->find();
            $receiverBeforeAsset = $targetUserAsset->toArray();
            
            // 创建转账记录
            $transferRecord = new \app\common\model\EsopTransferRecord();
            $transferRecord->user_id = $userId;
            $transferRecord->target_user_id = $targetUser->id;
            $transferRecord->amount = $amount;
            $transferRecord->remark = $remark;
            
            // 判断是否需要审批
            if ($transferApprovalSwitch == 1) {
                // 审批开关开启，资金锁定，等待审批
                $transferRecord->approval_status = 0; // 待审批状态
                $transferRecord->save();
                
                // 锁定当前用户资金
                $result = \app\common\model\EsopUserAsset::changeUserAsset($userId, [
                    'available_assets' => -$amount,
                    'locked_assets' => $amount,
                ]);
                
                if (!$result) {
                    throw new \Exception('锁定资金失败');
                }
                
                // 获取操作后的用户资产数据
                $senderAfterAsset = EsopUserAsset::where('user_id', $userId)->find()->toArray();
                
                // 记录资金变化（仅转出方）
                EsopUserFundChangeRecords::batchAddRecords([
                    [
                        'user_id' => $userId,
                        'amount' => $amount,
                        'action_type' => EsopUserFundChangeRecords::ACTION_TYPE_LOCK, // 锁定资金
                        'before_asset' => $senderBeforeAsset,
                        'after_asset' => $senderAfterAsset,
                        'remark' => '用户ID ' . $userId . ' 向用户ID ' . $targetUser->id . ' 转出待审批，资金锁定',
                        'transfer_record_id' => $transferRecord->id,
                        'extends' => ['user_id' => $userId, 'target_user_id' => $targetUser->id]
                    ]
                ]);

                // 记录资产变化日志
                \app\common\model\EsopAssetChangeLog::batchRecordAssetChange(
                    [
                        [
                            'user_id' => $userId,
                            'b_account_id' => 0,
                            'stock_id' => 0,
                            'amount' => -$amount,
                            'action_name' => '转出',
                            'extra_name' => $userProfile->real_name,
                            'sub_name' => '资产',
                            'is_audit' => 1,
                            'audit_status' => 0,
                            'extends' => ['user_id' => $userId, 'target_user_id' => $targetUser->id],
                            'relation_id' => $transferRecord->id
                        ],
                        [
                            'user_id' => $targetUser->id,
                            'b_account_id' => 0,
                            'stock_id' => 0,
                            'amount' => $amount,
                            'action_name' => '转出',
                            'extra_name' => Db::name('esop_user_profiles')->where('user_id', $targetUser->id)->value('real_name'),
                            'sub_name' => '资产',
                            'is_audit' => 1,
                            'audit_status' => 0,
                            'extends' => ['user_id' => $userId, 'target_user_id' => $targetUser->id],
                            'relation_id' => $transferRecord->id
                        ]
                    ]
                );
                
                \think\Db::commit();
                return $this->jsonSucess('转账申请已提交，等待审批', ['transfer_record_id' => $transferRecord->id]);
            } else {
                // 审批开关关闭，直接转账
                $transferRecord->approval_status = 1; // 已完成状态
                $transferRecord->save();
                
                // 扣减当前用户资产
                $result = \app\common\model\EsopUserAsset::changeUserAsset($userId, [
                    'available_assets' => -$amount,
                ]);
                
                if (!$result) {
                    throw new \Exception('扣减资金失败');
                }
                
                // 增加目标用户资产
                $result = \app\common\model\EsopUserAsset::changeUserAsset($targetUser->id, [
                    'available_assets' => $amount,
                ]);
                
                if (!$result) {
                    throw new \Exception('增加资金失败');
                }
                
                // 获取转账后的用户资产数据
                $senderAfterAsset = EsopUserAsset::where('user_id', $userId)->find()->toArray();
                $receiverAfterAsset = EsopUserAsset::where('user_id', $targetUser->id)->find()->toArray();
                
                // 批量记录资金变化（转出方和接收方）
                EsopUserFundChangeRecords::batchAddRecords([
                    [
                        'user_id' => $userId,
                        'amount' => $amount,
                        'action_type' => EsopUserFundChangeRecords::ACTION_TYPE_TRANSFER,
                        'before_asset' => $senderBeforeAsset,
                        'after_asset' => $senderAfterAsset,
                        'remark' => '用户ID ' . $userId . ' 向用户ID ' . $targetUser->id . ' 转出',
                        'transfer_record_id' => $transferRecord->id,
                        'extends' => ['user_id' => $userId, 'target_user_id' => $targetUser->id]
                    ],
                    [
                        'user_id' => $targetUser->id,
                        'amount' => $amount,
                        'action_type' => EsopUserFundChangeRecords::ACTION_TYPE_ACCEPT,
                        'before_asset' => $receiverBeforeAsset,
                        'after_asset' => $receiverAfterAsset,
                        'remark' => '用户ID ' . $targetUser->id . ' 接受用户ID ' . $userId . ' 转出',
                        'transfer_record_id' => $transferRecord->id,
                        'extends' => ['user_id' => $userId, 'target_user_id' => $targetUser->id]
                    ]
                ]);

                // 记录资产变化日志
                \app\common\model\EsopAssetChangeLog::batchRecordAssetChange(
                    [
                        [
                            'user_id' => $userId,
                            'b_account_id' => 0,
                            'stock_id' => 0,
                            'amount' => -$amount,
                            'action_name' => '转出',
                            'extra_name' => $userProfile->real_name,
                            'sub_name' => '资产',
                            'is_audit' => 0,
                            'audit_status' => 0,
                            'extends' => ['user_id' => $userId, 'target_user_id' => $targetUser->id],
                            'relation_id' => $transferRecord->id
                        ],
                        [
                            'user_id' => $targetUser->id,
                            'b_account_id' => 0,
                            'stock_id' => 0,
                            'amount' => $amount,
                            'action_name' => '转出',
                            'extra_name' => Db::name('esop_user_profiles')->where('user_id', $targetUser->id)->value('real_name'),
                            'sub_name' => '资产',
                            'is_audit' => 0,
                            'audit_status' => 0,
                            'extends' => ['user_id' => $userId, 'target_user_id' => $targetUser->id],
                            'relation_id' => $transferRecord->id
                        ]
                    ]
                );
                
                \think\Db::commit();
                return $this->jsonSucess('转出成功', ['transfer_record_id' => $transferRecord->id]);
            }
        } catch (\Exception $e) {
            \think\Db::rollback();
            return $this->error('转出失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取用户资产变化列表
     * 
     * @ApiTitle    (获取用户资产变化列表)
     * @ApiSummary  (获取当前登录用户的资产变化记录列表)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"**********","data":{"total":1,"per_page":10,"current_page":1,"last_page":1,"data":[...]}})
     */
    public function userAssetsChangeList()
    {
        $userId = $this->auth->id;
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        // 使用模型方法获取用户资产变化日志
        $list = EsopAssetChangeLog::alias('acl')
            ->join('esop_user_profiles up', 'acl.user_id = up.user_id', 'LEFT')
            ->field([
                'acl.id',
                'acl.amount',
                'acl.action_name',
                'acl.extra_name',
                'acl.sub_name',
                'acl.is_audit',
                'acl.audit_status',
                'acl.extends',
                'acl.created_at',
                'up.real_name',
                'up.nickname'
            ])
            ->where('acl.user_id', $userId)
            ->order('acl.created_at', 'desc')
            ->paginate($limit, false, ['page' => $page]);

        $rows = [];
        foreach ($list->items() as $item) {
            $row = [
                'id' => $item['id'],
                'amount' => $item['amount'],
                'action_name' => $item['action_name'],
                'extra_name' => $item['extra_name'] ?: '',
                'sub_name' => $item['sub_name'] ?: '',
                'is_audit' => $item['is_audit'],
                'audit_status' => $item['audit_status'],
                'audit_status_text' => EsopAssetChangeLog::getAuditStatusText($item['audit_status']),
                'is_audit_text' => EsopAssetChangeLog::getIsAuditText($item['is_audit']),
                'real_name' => $item['real_name'] ?: '',
                'nickname' => $item['nickname'] ?: '',
                'created_at' => $item['created_at'],
            ];

            // 处理扩展信息
            if (!empty($item['extends'])) {
                $extends = json_decode($item['extends'], true);
                $row['extends'] = $extends;
            } else {
                $row['extends'] = [];
            }

            $rows[] = $row;
        }

        $data = [
            'total' => $list->total(),
            'per_page' => $list->listRows(),
            'current_page' => $list->currentPage(),
            'last_page' => $list->lastPage(),
            'rows' => $rows
        ];

        return $this->success('获取成功', $data);
    }
}
