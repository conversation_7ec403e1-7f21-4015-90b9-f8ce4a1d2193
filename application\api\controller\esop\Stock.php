<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopStockManagement;
use think\Exception;
use app\common\model\EsopExchangeRecords;
use app\common\model\EsopUserAsset; // 添加用户资产模型引入
use app\common\model\EsopUserFundChangeRecords; // 添加用户资金变化记录模型引入
use app\common\model\EsopUserStockRecords; // 添加用户股票记录模型
use app\common\model\EsopExerciseRecords; // 添加行权记录模型引入
use app\common\library\Stock as StockLibrary; // 添加股票价格库引入

/**
 * 股票管理接口
 */
class Stock extends Api
{
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = [];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];

    // 模型对象
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopStockManagement();
    }

    /**
     * 股票列表
     * 
     * @ApiTitle    (获取股票列表)
     * @ApiSummary  (分页获取股票数据)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="0")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="返回成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'total':1,'list':[{'id':1,'stock_code':'601398','stock_name':'工商银行','stock_price':5.8}]}")
     */
    public function index()
    {
        // 获取参数
        $page  = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        try {
            $where = ['is_on_shelf' => 1];

            // 查询数据并分页
            $pageData = $this->model
                ->where($where)
                ->field(['id','stock_code','stock_name','stock_unit','stock_price','price_type','average_time','exchangeable_amount','is_on_shelf'])
                ->order('id DESC')
                ->paginate($limit, false, ['page' => $page]);

            // 处理每条数据，获取最新价格
            $list = [];
            foreach ($pageData->items() as $item) {
                $row = $item->toArray();
                $latestPrice = null;
                if ($row['price_type'] == 1) {
                    // 均价类型
                    $averageDays = $row['average_time'] ?: 5;
                    $latestPrice = \app\common\library\Stock::getAveragePrice($row['stock_code'], $averageDays);
                } elseif ($row['price_type'] == 2) {
                    // 约定价类型
                    $latestPrice = \app\common\library\Stock::getLatestStockPrice($row['stock_code']);
                }
                $row['latest_price'] = $latestPrice;
                $list[] = $row;
            }

            $this->success('', [
                'total' => $pageData->total(),
                'list'  => $list
            ]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 发起换股
     * 
     * @ApiTitle    (发起换股)
     * @ApiSummary  (用户发起换股请求)
     * @ApiMethod   (POST)
     * @ApiParams   (name="stock_id", type="integer", required=true, description="股票ID")
     * @ApiParams   (name="amount", type="number", required=true, description="数量")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="0")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="返回成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'id':1}")
     */
    public function exchange()
    {
        // 获取参数
        $params = $this->request->post();
        
        // 验证参数
        $rules = [
            'stock_id' => 'require|number',
            'amount' => 'require|float|gt:0',
        ];
        
        $messages = [
            'stock_id.require' => '股票ID不能为空',
            'stock_id.number' => '股票ID必须为数字',
            'amount.require' => '数量不能为空',
            'amount.float' => '数量必须为数字',
            'amount.gt' => '数量必须大于0',
        ];
        
        $validate = new \think\Validate($rules);
        $validate->message($messages);
        
        if (!$validate->check($params)) {
            $this->error($validate->getError());
        }
        
        try {
            // 开启事务
            \think\Db::startTrans();
            
            // 查询股票信息
            $stockInfo = $this->model->where(['id' => $params['stock_id'], 'is_on_shelf' => 1])->find();
            if (!$stockInfo) {
                $this->error('股票不存在或未上架');
            }

            // 检查可兑换数量
            if ($stockInfo['exchangeable_amount'] < $params['amount']) {
                $this->error('可兑换数量不足');
            }
            
            // 验证数量是否为股票单位的整数倍
            if (fmod($params['amount'], $stockInfo['stock_unit']) != 0) {
                $this->error('兑换数量必须是股票单位('.$stockInfo['stock_unit'].')的整数倍');
            }
            
            // 检查用户资产是否足够
            $userId = $this->auth->id;
            $userAssetModel = new EsopUserAsset();
            $userAsset = $userAssetModel->where('user_id', $userId)->find();
            
            if (!$userAsset) {
                $this->error('用户资产信息不存在');
            }
            
            // 获取用户当前持有该股票的数量
            $userStock = EsopUserStockRecords::where(['user_id' => $userId, 'stock_id' => $stockInfo['id']])->find();
            $beforeStockAmount = $userStock ? $userStock['amount'] : 0;
            $afterStockAmount = bcadd($beforeStockAmount, $params['amount'], 8);

            // 记录换股前后的可兑换数量
            $beforeExchangeableAmount = $stockInfo['exchangeable_amount'];
            $afterExchangeableAmount = bcsub($beforeExchangeableAmount, $params['amount'], 8);

            // 计算换股金额
            // 使用bc库进行高精度浮点数计算，确保金额计算的精确性
            // bcadd/bcmul等函数返回字符串类型
            $exchangeAmount = bcmul((string)$params['amount'], (string)$stockInfo['stock_price'], 8); // 保留8位小数
            
            // 使用bccomp函数进行高精度浮点数比较，确保资产比较的精确性
            // bccomp返回-1表示第一个数小于第二个数
            if (bccomp($userAsset['available_assets'], $exchangeAmount, 8) < 0) {
                $this->error('可兑换资产不足，无法完成换股');
            }
            
            // 创建换股记录
            $exchangeModel = new EsopExchangeRecords();
            $exchangeData = [
                'user_id' => $userId,
                'stock_id' => $stockInfo['id'],
                'amount' => $params['amount'],
                'stock_price' => $stockInfo['stock_price'],
                'stock_price_type' => $stockInfo['price_type'],
                'stock_average_time' => $stockInfo['average_time'],
                'exchange_amount' => $exchangeAmount,
                'before_stock_amount' => $beforeStockAmount,
                'after_stock_amount' => $afterStockAmount,
                'before_exchangeable_amount' => $beforeExchangeableAmount,
                'after_exchangeable_amount' => $afterExchangeableAmount,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $exchangeId = $exchangeModel->insertGetId($exchangeData);
            
            // 更新股票可兑换数量
            $this->model->where('id', $stockInfo['id'])->where('exchangeable_amount', '>=', $params['amount'])->setDec('exchangeable_amount', $params['amount']);

            // ===================== 新增逻辑：更新用户股票记录 =====================
            // 将兑换的股票数量计入用户股票表
            EsopUserStockRecords::changeUserStock($userId, $stockInfo['id'], $params['amount']);
            // =====================================================================
            
            // 获取操作前的用户资产数据
            $beforeUserAsset = $userAsset->toArray();
            
            // 更新用户资产
            EsopUserAsset::changeUserAsset($userId, [
                'available_assets' => -$exchangeAmount,
            ]);
            
            // 获取操作后的用户资产数据
            $afterUserAsset = $userAssetModel->where('user_id', $userId)->find()->toArray();
            
            // 使用模型添加资金变化记录
            EsopUserFundChangeRecords::addRecord(
                $userId,
                $exchangeAmount,
                EsopUserFundChangeRecords::ACTION_TYPE_EXCHANGE, // 换股
                $beforeUserAsset,
                $afterUserAsset,
                '换股' . $stockInfo['stock_name'] . '(' . $stockInfo['stock_code'] . ')',
                ['stock_name' => $stockInfo['stock_name'], 'stock_code' => $stockInfo['stock_code']]
            );

            // 记录资产变化日志
            \app\common\model\EsopAssetChangeLog::batchRecordAssetChange(
                [
                    [
                        'user_id' => $userId,
                        'b_account_id' => 0,
                        'stock_id' => $stockInfo['id'],
                        'amount' => $params['amount'],
                        'action_name' => '换股',
                        'extra_name' => $stockInfo['stock_name'],
                        'sub_name' => '股票',
                        'is_audit' => 0,
                        'audit_status' => 0,
                        'extends' => ['stock_name' => $stockInfo['stock_name'], 'stock_code' => $stockInfo['stock_code']],
                        'relation_id' => $exchangeId
                    ],
                    [
                        'user_id' => $userId,
                        'b_account_id' => 0,
                        'stock_id' => $stockInfo['id'],
                        'amount' => -$exchangeAmount, // 扣减资产
                        'action_name' => '换股',
                        'extra_name' => $stockInfo['stock_name'],
                        'sub_name' => '资产',
                        'is_audit' => 0,
                        'audit_status' => 0,
                        'extends' => ['stock_name' => $stockInfo['stock_name'], 'stock_code' => $stockInfo['stock_code']],
                        'relation_id' => $exchangeId
                    ]   
                ]
            );
            
            // 提交事务
            \think\Db::commit();
            
            $this->success('换股申请成功', ['id' => $exchangeId]);
        } catch (Exception $e) {
            // 回滚事务
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取换股记录
     * 
     * @ApiTitle    (获取换股记录)
     * @ApiSummary  (分页获取用户的换股记录)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="0")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="返回成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'total':1,'list':[{'id':1,'stock_id':1,'amount':100,'stock_price':5.8,'exchange_amount':580}]}")
     */
    public function exchangeList()
    {
        // 获取参数
        $page  = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);
        
        try {
            // 获取当前登录用户ID
            $userId = $this->auth->id;
            
            // 创建换股记录模型
            $exchangeModel = new EsopExchangeRecords();
            
            // 查询数据并分页
            $pageData = $exchangeModel
                ->with(['stock' => function($query) {
                    // 需要包含 price_type 字段, 否则 Stock 模型中的 getPriceTypeTextAttr 会因缺少 price_type 而报错
                    $query->field(['id', 'stock_code', 'stock_name', 'price_type','is_on_shelf']);
                }])
                ->where('user_id', $userId)
                ->field([
                    'id', 'stock_id', 'amount', 'stock_price', 
                    'stock_price_type', 'exchange_amount', 'created_at'
                ])
                ->order('id DESC')
                ->paginate($limit, false, ['page' => $page]);
            
            // 处理返回结果，只返回所需字段
            $list = [];
            foreach ($pageData->items() as $row) {
                // 转换为数组，便于取值
                $data = $row->toArray();

                $list[] = [
                    'stock_code' => $data['stock']['stock_code'] ?? '', // 股票代码
                    'amount' => $data['amount'],                        // 兑换数量 
                    'stock_name' => $data['stock']['stock_name'] ?? '', // 股票名称
                    'stock_price' => $data['stock_price'],               // 价格
                    'exchange_amount' => $data['exchange_amount'],      // 兑换金额
                    'created_at' => $data['created_at'],                 // 创建时间
                ];
            }
            
            $this->success('获取成功', [
                'total' => $pageData->total(),
                'list'  => $list
            ]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 股票行权
     * 
     * @ApiTitle    (进行行权)
     * @ApiSummary  (用户对持有股票进行行权)
     * @ApiMethod   (POST)
     * @ApiParams   (name="stock_id", type="integer", required=true, description="股票ID")
     * @ApiParams   (name="amount", type="number", required=true, description="行权数量")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="1")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="行权成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'id':1}")
     */
    public function exercise()
    {
        // 获取参数
        $params = $this->request->post();

        // 参数校验规则
        $rules = [
            'stock_id' => 'require|number',
            'amount' => 'require|float|gt:0',
        ];
        $messages = [
            'stock_id.require' => '股票ID不能为空',
            'stock_id.number' => '股票ID必须为数字',
            'amount.require' => '行权数量不能为空',
            'amount.float' => '行权数量必须为数字',
            'amount.gt' => '行权数量必须大于0',
        ];
        $validate = new \think\Validate($rules);
        $validate->message($messages);
        if (!$validate->check($params)) {
            $this->error($validate->getError());
        }

        // 获取当前用户ID
        $userId = $this->auth->id;

        // 开启事务，保证数据一致性
        \think\Db::startTrans();
        try {
            // 查询股票信息
            $stockInfo = $this->model->where(['id' => $params['stock_id'], 'is_on_shelf' => 1])->find();
            if (!$stockInfo) {
                $this->error('股票不存在或未上架');
            }

            // 查询用户持有的该股票数量
            $userStock = \app\common\model\EsopUserStockRecords::where(['user_id' => $userId, 'stock_id' => $params['stock_id']])->find();
            if (!$userStock || bccomp($userStock['amount'], $params['amount'], 8) < 0) {
                $this->error('持有股票数量不足，无法行权');
            }

            // 记录行权前股票数量
            $beforeStockAmount = $userStock['amount'];
            // 计算行权后股票数量
            $afterStockAmount = bcsub($beforeStockAmount, $params['amount'], 8);

            // 扣减用户持有股票数量
            $changeResult = \app\common\model\EsopUserStockRecords::changeUserStock($userId, $params['stock_id'], -$params['amount']);
            if (!$changeResult) {
                throw new Exception('扣减股票失败');
            }

            // 计算行权金额 = 行权数量 * 股票价格
            $exerciseAmount = bcmul((string)$params['amount'], (string)$stockInfo['stock_price'], 8);
            
            // 获取用户所属的B端账号及其行权手续费率
            // 先查询用户是否为B端账户管理员
            $bAccount = \app\common\model\EsopBAccount::whereRaw("FIND_IN_SET({$userId}, user_ids)")
                ->find();
            
            // 如果不是B端管理员，则查询是否为B端团队成员
            if (!$bAccount) {
                $bAccountId = \app\common\model\EsopInvitationRelation::where('invitee_id', $userId)
                    ->value('b_account_id');
                if ($bAccountId) {
                    $bAccount = \app\common\model\EsopBAccount::where('id', $bAccountId)
                        ->find();
                }
            }

            if (!$bAccount) {
                $this->error('用户不是B端账户管理员或团队成员');
            }
            
            // 获取行权手续费率（默认为0）
            $exerciseFeeRate = $bAccount ? $bAccount['exercise_fee_rate'] : 0;
            // 计算行权手续费 = 行权金额 * 行权手续费率（百分比）
            $exerciseFee = bcmul($exerciseAmount, bcdiv($exerciseFeeRate, 100, 8), 8);

            // 写入行权记录表（fa_esop_exercise_records）
            $exerciseRecord = new EsopExerciseRecords();
            $exerciseRecord->data([
                'user_id' => $userId,
                'stock_id' => $params['stock_id'],
                'amount' => $params['amount'],
                'stock_price' => $stockInfo['stock_price'],
                'stock_price_type' => $stockInfo['price_type'],
                'stock_average_time' => $stockInfo['average_time'],
                'exercise_fee' => $exerciseFee,
                'exercise_amount' => $exerciseAmount,
                'before_stock_amount' => $beforeStockAmount,
                'after_stock_amount' => $afterStockAmount,
                'approval_status' => 0,
                'approval_remark' => '',
                'approval_time' => null,
                'approval_user_id' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
            $exerciseRecord->save();
            $exerciseRecordId = $exerciseRecord->id;

            // 记录资产变化日志
            \app\common\model\EsopAssetChangeLog::batchRecordAssetChange(
                [
                    [
                        'user_id' => $userId,
                        'b_account_id' => 0,
                        'stock_id' => $params['stock_id'],
                        'amount' => -$params['amount'],
                        'action_name' => '行权',
                        'extra_name' => $stockInfo['stock_name'],
                        'sub_name' => '股票',
                        'is_audit' => 1,
                        'audit_status' => 0,
                        'extends' => ['stock_name' => $stockInfo['stock_name'], 'stock_code' => $stockInfo['stock_code']],
                        'relation_id' => $exerciseRecordId
                    ]
                ]
            );

            // 提交事务
            \think\Db::commit();
            $this->success('行权成功', ['id' => $exerciseRecordId]);
        } catch (Exception $e) {
            // 回滚事务
            \think\Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取行权记录
     * 
     * @ApiTitle    (获取行权记录)
     * @ApiSummary  (分页获取用户的行权记录)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="1")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="获取成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'total':1,'list':[{'stock_code':'000001','stock_name':'平安银行','stock_price':10.00,'exercise_amount':10000,'created_at':'2025-01-01 12:00:00'}]}")
     */
    public function exerciseList()
    {
        // 获取参数
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);
        
        try {
            // 获取当前登录用户ID
            $userId = $this->auth->id;
            
            // 查询行权记录数据并分页
            $pageData = EsopExerciseRecords::with(['stock' => function($query) {
                    // 需要包含必要字段，避免模型获取器报错
                    $query->field(['id', 'stock_code', 'stock_name', 'price_type', 'is_on_shelf']);
                }])
                ->where('user_id', $userId)
                ->field([
                    'id', 'stock_id', 'amount', 'stock_price', 'stock_price_type',
                    'exercise_amount', 'exercise_fee', 'approval_status', 'created_at'
                ])
                ->order('id DESC')
                ->paginate($limit, false, ['page' => $page]);
            
            // 处理返回结果，只返回所需字段
            $list = [];
            foreach ($pageData->items() as $row) {
                // 转换为数组，便于取值
                $data = $row->toArray();
                
                // 添加审批状态文本
                $approvalStatusText = '';
                switch ($data['approval_status']) {
                    case 0:
                        $approvalStatusText = '待审批';
                        break;
                    case 1:
                        $approvalStatusText = '已通过';
                        break;
                    case 2:
                        $approvalStatusText = '已拒绝';
                        break;
                    default:
                        $approvalStatusText = '未知状态';
                }
                
                $list[] = [
                    'stock_code' => $data['stock']['stock_code'] ?? '', // 股票代码
                    'stock_name' => $data['stock']['stock_name'] ?? '', // 股票名称
                    'stock_price' => $data['stock_price'],              // 股票价格
                    'exercise_amount' => $data['exercise_amount'],      // 行权金额
                    'amount' => $data['amount'],                        // 行权数量
                    'exercise_fee' => $data['exercise_fee'],            // 行权手续费
                    'approval_status' => $data['approval_status'],      // 审批状态
                    'approval_status_text' => $approvalStatusText,      // 审批状态文本
                    'created_at' => $data['created_at'],                // 创建时间
                ];
            }
            
            $this->success('获取成功', [
                'total' => $pageData->total(),
                'list' => $list
            ]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取用户股票持有列表
     * 
     * @ApiTitle    (获取用户股票持有列表)
     * @ApiSummary  (分页获取用户的股票持有列表)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="0")  
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="返回成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'total':1,'list':[{'stock_code':'000001','stock_name':'平安银行','stock_price':10.00,'stock_amount':10000,'created_at':'2025-01-01 12:00:00'}]}")
     */
    public function userStockList()
    {
        // 获取参数
        $page = $this->request->get('page/d', 1);
        $limit = $this->request->get('limit/d', 10);

        try {
            // 获取当前登录用户ID
            $userId = $this->auth->id;

            // 查询用户股票持有列表
            $pageData = EsopUserStockRecords::with(['stock' => function($query) {
                $query->field(['id', 'stock_code', 'stock_price','stock_name', 'price_type', 'is_on_shelf', 'average_time']); 
            }])
            ->where('user_id', $userId)
            ->field([
                'id', 'stock_id', 'amount', 
                'created_at'
            ])
            ->order('id DESC')
            ->paginate($limit, false, ['page' => $page]);   

            // 处理返回结果，只返回所需字段
            $rows = [];
            foreach ($pageData->items() as $row) {
                // 转换为数组，便于取值
                $data = $row->toArray();
                
                $rows[] = [ 
                    'stock_id' => $data['stock']['id'],
                    'stock_code' => $data['stock']['stock_code'] ?? '', // 股票代码
                    'stock_name' => $data['stock']['stock_name'] ?? '', // 股票名称
                    'stock_price' => $data['stock']['stock_price'],               // 股票价格
                    'stock_amount' => $data['amount'],                   // 股票数量
                    'created_at' => $data['created_at'],                 // 创建时间
                ];
            }
            
            $this->success('获取成功', [    
                'per_page' => $pageData->listRows(),
                'current_page' => $pageData->currentPage(),
                'last_page' => $pageData->lastPage(),
                'total' => $pageData->total(),
                'rows' => $rows
            ]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 根据股票ID获取最新价格
     *
     * @ApiTitle    (获取股票最新价格)
     * @ApiSummary  (根据股票ID和价格类型获取最新的股票价格)
     * @ApiMethod   (GET)
     * @ApiParams   (name="stock_id", type="integer", required=true, description="股票ID")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="1")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="获取成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'stock_id':1,'stock_code':'HK0080','stock_name':'中国海外发展','price_type':1,'price_type_text':'均价','current_price':5.8,'latest_price':5.9,'average_time':5}")
     */
    public function getLatestPrice()
    {
        // 获取参数
        $stockId = $this->request->get('stock_id/d', 0);

        // 参数验证
        if (!$stockId) {
            $this->error('股票ID不能为空');
        }

        try {
            // 查询股票信息
            $stockInfo = $this->model->where(['id' => $stockId])->find();
            if (!$stockInfo) {
                $this->error('股票不存在');
            }

            // 转换为数组便于操作
            $stockData = $stockInfo->toArray();

            // 根据价格类型获取最新价格
            $latestPrice = null;
            $priceSource = '';

            if ($stockData['price_type'] == 1) {
                // 均价类型：调用getAveragePrice函数
                $averageDays = $stockData['average_time'] ?: 5; // 默认5天
                $latestPrice = StockLibrary::getAveragePrice($stockData['stock_code'], $averageDays);
                $priceSource = "最近{$averageDays}天均价";
            } elseif ($stockData['price_type'] == 2) {
                // 约定价类型：调用getLatestStockPrice函数
                $latestPrice = StockLibrary::getLatestStockPrice($stockData['stock_code']);
                $priceSource = '最新实时价格';
            }

            // 构建返回数据
            $result = [
                'stock_id' => $stockData['id'],
                'stock_code' => $stockData['stock_code'],
                'stock_name' => $stockData['stock_name'],
                'price_type' => $stockData['price_type'],
                'price_type_text' => $stockData['price_type'] == 1 ? '均价' : '约定价',
                'current_price' => $stockData['stock_price'], // 当前系统中的价格
                'latest_price' => $latestPrice, // 从接口获取的最新价格
                'price_source' => $priceSource,
                'average_time' => $stockData['average_time'],
                'is_price_updated' => false, // 是否价格有更新
                'price_change' => null, // 价格变化
                'price_change_percent' => null // 价格变化百分比
            ];

            // 如果成功获取到最新价格，计算价格变化
            if ($latestPrice !== null) {
                $currentPrice = floatval($stockData['stock_price']);
                $newPrice = floatval($latestPrice);

                if ($currentPrice > 0) {
                    $result['is_price_updated'] = ($newPrice != $currentPrice);
                    $result['price_change'] = round($newPrice - $currentPrice, 4);
                    $result['price_change_percent'] = round(($newPrice - $currentPrice) / $currentPrice * 100, 2);
                }
            }

            $this->success('获取成功', $result);

        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
}